<?php

namespace App\Console\Commands;

use App\Mail\ReminderCandidateMail;
use App\Models\Civility;
use App\Models\Role;
use App\Models\ReminderCandidate;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class ReminderCandidateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:reminder-candidate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $users = User::where('role_id', Role::where('slug', 'candidate')->first()->id)
            ->get();

        foreach ($users as $user) {
            $reminder_candidate = ReminderCandidate::where('user_id', $user->id)->first();

            if($reminder_candidate)
            {
                // si le candidat n'a pas repondu a l'email
                if($reminder_candidate->search_work == null)
                {
                    // si creer depuis 20 minutes
                    if($user->created_at->diffInDays(now()) >= 14)
                    {
                        $civility = Civility::where('user_id', $user->id)->first();
                        $civility->visibility = '0';
                        $civility->save();

                        var_dump('Visibility set to 0');
                    }
                }
            }
            else
            {
                // si creer depuis 15 minutes
                if($user->created_at->diffInDays(now()) >= 12)
                {
                    $reminder_candidate = ReminderCandidate::create([
                        'user_id' => $user->id,
                        'search_work' => null
                    ]);
                    Mail::to($user->email)->send(new ReminderCandidateMail($reminder_candidate));
                    var_dump('Email sent after 15 minutes');
                }
            }

        }
    }
}
