<div id="drawer-navigation"
    class="fixed top-0 left-0 z-40 w-240 h-screen p-4 overflow-y-auto transition-transform -translate-x-full bg-white dark:bg-gray-800"
    tabindex="-1" aria-labelledby="drawer-navigation-label">
    <h5 id="drawer-navigation-label" class="text-base font-semibold text-gray-500 uppercase dark:text-gray-400">Menu
    </h5>
    <button type="button" data-drawer-hide="drawer-navigation" aria-controls="drawer-navigation"
        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 absolute top-2.5 end-2.5 inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white">
        <svg aria-hidden="true" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clip-rule="evenodd"></path>
        </svg>
        <span class="sr-only">Fermer le menu</span>
    </button>
    <div class="py-4 overflow-y-auto">
        <ul class="space-y-2 font-medium">
            <li>
                <a href="{{ route('admin.index') }}"
                    class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                    <svg class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 22 21">
                        <path
                            d="M16.975 11H10V4.025a1 1 0 0 0-1.066-.998 8.5 8.5 0 1 0 9.039 9.039.999.999 0 0 0-1-1.066h.002Z" />
                        <path
                            d="M12.5 0c-.157 0-.311.01-.565.027A1 1 0 0 0 11 1.02V10h8.975a1 1 0 0 0 1-.935c.013-.188.028-.374.028-.565A8.51 8.51 0 0 0 12.5 0Z" />
                    </svg>
                    <span class="ms-3">
                        Tableau de bord
                    </span>
                </a>
            </li>
            <li>
                <a href="{{ route('admin.recruiters') }}"
                    class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                    <svg fill="currentColor"
                        class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                        viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M856 752L645 540q-4-4-10-2.5t-8 7.5l-94 263-71 25q-6 2-7 8t3 11l87 87q4 4 10.5 2.5t8.5-6.5l25-71 263-94q6-2 7.5-8t-3.5-10zM418 894q-13-12-18.5-28.5T397 831t15.5-32.5T443 778l31-11q13-5 17-18l45-124q4-12-3-23l-34-51q-6-8-14-15-2-2-1.5-4.5t2.5-3.5q60-29 98-83 39-58 39-124 0-63-31-116t-84-84-115.5-31T277 121t-84 84-31 116q0 66 39 124 37 54 98 83 2 1 2 3.5t-2 4.5q-7 7-13 15-88 131-132 205-18 32-18 52 0 27 34.5 50.5T264 896t128 14h19q6 0 8.5-6t-1.5-10zm434-393l-45 60q-3 3-2.5 7t3.5 7q8 7 16 15 2 3 6 3.5t7-1.5l61-44q3-3 4-7t-2-8q-15-18-34-34-3-3-7.5-2.5T852 501zm7 160q2 10 2 20 1 4 3.5 6.5t6.5 2.5h75q5 0 8-3t2-7q-1-23-6-45-1-4-4.5-6t-7.5-1l-73 22q-3 1-5 4t-1 7zM749 532l22-72q2-4 0-7.5t-6-4.5q-24-6-48-8-4 0-7 3t-3 7v76q0 3 2.5 6t6.5 3q11 1 22 4 3 1 6.5-1t4.5-6z">
                            </path>
                        </g>
                    </svg>
                    <span class="flex-1 ms-3 whitespace-nowrap">
                        Recruteurs
                    </span>
                    <span
                        class="inline-flex items-center justify-center px-2 ms-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">
                        @php
                            $role_id = \App\Models\Role::where('slug', 'recruter')->first()->id;
                            $recruiterCount = \App\Models\User::where('role_id', $role_id)->count();
                        @endphp
                        {{ $recruiterCount }}
                    </span>
                </a>
            </li>
            <li>
                <a href="{{ route('admin.candidates') }}"
                    class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                    <svg fill="currentColor"
                        class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                        version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 125.023 125.023" xml:space="preserve">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <g>
                                <g>
                                    <path
                                        d="M65.176,57.92c16,0,28.952-12.972,28.952-28.962C94.128,12.963,81.176,0,65.176,0C49.183,0,36.218,12.964,36.218,28.958 C36.218,44.949,49.183,57.92,65.176,57.92z">
                                    </path>
                                    <path
                                        d="M72.632,59.087l-7.211,7.264l-6.993-7.34c-0.024,0.006-0.05,0.006-0.066,0.012c-1.167,0.28-6.12,1.856-12.546,6.465 c-0.057,0.04-1.362,0.945-1.973,1.328c-1.213,0.766-3.024,1.875-5.215,2.922c-2.178,1.064-4.758,2.027-7.106,2.531 c-1.159,0.23-2.206,0.293-3.047,0.266c-0.869-0.016-1.369-0.204-1.899-0.436c-0.285-0.066-0.496-0.334-0.808-0.482 c-0.244-0.324-0.597-0.479-0.862-0.939c-0.142-0.203-0.305-0.373-0.457-0.593l-0.411-0.761c-0.318-0.452-0.519-1.126-0.776-1.706 c-0.281-0.558-0.426-1.292-0.635-1.935c-0.218-0.637-0.364-1.336-0.491-2.037c-0.322-1.348-0.473-2.755-0.63-4.047 c-0.193-1.274-0.181-2.553-0.276-3.632c-0.003-0.031-0.001-0.058-0.003-0.089c0.613-0.878,1.446-1.67,2.459-2.405 c1.012-0.727,1.808-1.937,2.336-3.094c2.054-4.563,2.947-7.176,4.421-11.962c0.622-2.016-3.096-4.247-5.522,1.459 c-1.026,2.067-0.578,2.279-1.621,4.338l-0.373,0.701c0,0-0.215-1.988-0.243-2.589c-0.323-6.89-0.618-10.586-0.949-17.476 c-0.09-1.911-0.886-2.762-2.361-2.66c-1.404,0.101-2.021,0.966-1.946,2.823c0.151,3.761,0.331,4.323,0.483,8.081 c0.071,1.417-0.851,1.148-0.845-0.006c-0.244-5.126-0.477-6.258-0.683-11.385c-0.058-1.392-0.637-2.305-2.064-2.458 c-1.379-0.146-2.321,0.999-2.251,2.742c0.205,4.955,0.45,5.915,0.654,10.871c0.072,1.466-0.83,1.235-0.833,0.133 c-0.183-3.928-0.299-4.667-0.583-8.588c-0.055-0.79-0.535-1.828-1.156-2.242c-1.515-1.009-3.171,0.277-3.101,2.369 c0.146,4.387,0.383,5.577,0.564,9.96c0.109,1.125-0.772,1.427-0.82,0.117c-0.136-2.791-0.241-2.389-0.394-5.177 c-0.07-1.271-0.794-1.997-2.072-2.046c-1.291-0.047-2.002,0.704-2.212,1.918c-0.09,0.497-0.042,1.022-0.019,1.531 c0.294,6.608,0.471,10.029,0.959,16.622c0.174,2.309,0.451,3.921,0.829,5.785c0.378,1.864,1.418,2.743,1.667,3.666 c-0.058,1.068-0.128,2.19-0.086,3.477c0.023,1.71,0.033,3.558,0.27,5.615c0.082,1.012,0.19,2.062,0.416,3.182 c0.215,1.114,0.345,2.219,0.72,3.428c0.348,1.197,0.616,2.388,1.18,3.666c0.259,0.63,0.52,1.264,0.783,1.9 c0.312,0.643,0.69,1.293,1.051,1.939c0.659,1.296,1.715,2.576,2.692,3.828c1.162,1.193,2.332,2.404,3.784,3.361 c2.788,1.992,6.115,3.328,9.163,3.834c3.063,0.549,5.932,0.553,8.498,0.308c0.689-0.077,1.532-0.168,2.192-0.269l0.019,33.848 h59.882v-12.961c1.321,3.738,2.566,8.053,3.745,12.961h23.102C116.131,93.336,98.253,67.534,72.632,59.087z M65.487,123.662 h-0.128l-6.987-9.557l6.987-46.678h0.128l6.992,46.678L65.487,123.662z">
                                    </path>
                                </g>
                            </g>
                        </g>
                    </svg>
                    <span class="flex-1 ms-3 whitespace-nowrap">
                        Candidats
                    </span>
                    <span
                        class="inline-flex items-center justify-center px-2 ms-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">
                        @php
                            $role_id = \App\Models\Role::where('slug', 'candidate')->first()->id;
                            $recruiterCount = \App\Models\User::where('role_id', $role_id)->count();
                        @endphp
                        {{ $recruiterCount }}
                    </span>
                </a>
            </li>
            <li>
                <a href="{{ route('admin.activities') }}"
                    class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                    <svg fill="currentColor"
                        class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path
                            d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0Zm0 18.75c-4.688 0-8.75-3.063-8.75-8.75S5.312 1.25 10 1.25 18.75 4.312 18.75 10 14.688 18.75 10 18.75ZM10 3.75c-3.75 0-6.25 2.5-6.25 6.25 0 3.75 2.5 6.25 6.25 6.25 3.75 0 6.25-2.5 6.25-6.25 0-3.75-2.5-6.25-6.25-6.25Z">
                        </path>
                    </svg>
                    <span class="ms-3">
                        Domaines d'activités
                    </span>
                </a>
            </li>
            <li>
                <a href="{{ route('admin.profession') }}"
                    class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                    <svg fill="currentColor"
                        class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                        version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512.002 512.002" xml:space="preserve">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <g>
                                <g>
                                    <g>
                                        <path
                                            d="M317.143,444.752c-0.775-3.101-3.562-5.284-6.759-5.284H201.616c-3.197,0-5.985,2.182-6.759,5.283 c-7.876,31.546-12.131,48.723-14.469,58.702c-1.024,4.372,2.294,8.547,6.784,8.547h137.654c4.489,0,7.807-4.174,6.784-8.544 C329.291,493.545,325.046,476.404,317.143,444.752z">
                                        </path>
                                        <path
                                            d="M189.856,352.876c-17.271-6.304-33.919-15.116-49.648-26.949c-2.971-2.236-7.162-1.776-9.547,1.077L37.381,438.586 c-24.366,29.146-3.791,73.415,35.301,73.415h78.805c3.29,0,6.124-2.306,6.812-5.524c3.059-14.314,13.917-57.282,35.927-145.371 C195.09,357.65,193.203,354.097,189.856,352.876z">
                                        </path>
                                        <path
                                            d="M297.659,366.797c-0.887-3.55-4.37-5.81-7.973-5.168c-21.566,3.844-44.191,4.132-67.372,0 c-3.603-0.642-7.085,1.619-7.973,5.168c-3.908,15.641-7.404,29.622-10.523,42.097l-0.256,1.023 c-1.099,4.397,2.226,8.655,6.756,8.655h91.36c4.529,0,7.858-4.247,6.761-8.641c-0.116-0.467-0.234-0.933-0.351-1.404 C304.995,396.148,301.529,382.288,297.659,366.797z">
                                        </path>
                                        <path
                                            d="M474.622,438.586l-93.281-111.583c-2.385-2.852-6.575-3.311-9.547-1.077c-15.087,11.349-31.622,20.37-49.649,26.949 c-3.348,1.222-5.234,4.774-4.37,8.232c21.979,87.96,32.931,131.32,35.928,145.396c0.684,3.21,3.527,5.497,6.808,5.497h78.809 C478.459,512,498.966,467.703,474.622,438.586z">
                                        </path>
                                        <path
                                            d="M101.086,110.267c2.691-0.501-5.292-0.35,115.908-0.35c11.54,0,20.896,9.356,20.896,20.896v20.626 c0,1.994,1.616,3.608,3.608,3.608h29.004c1.992,0,3.608-1.616,3.608-3.608v-20.626c0-11.54,9.356-20.896,20.896-20.896 c120.981,0,113.202-0.156,115.91,0.35c2.797,0.521,5.019-2.296,3.948-4.933C357.801-35.119,154.222-35.103,97.14,105.332 C96.067,107.969,98.289,110.786,101.086,110.267z">
                                        </path>
                                        <path
                                            d="M410.567,247.156c1.191-2.432-0.66-5.184-3.368-5.174c-0.025,0-0.05,0-0.077,0H295.007 c-11.54,0-20.896-9.356-20.896-20.896v-20.637c0-1.993-1.616-3.608-3.608-3.608h-29.004c-1.992,0-3.608,1.615-3.608,3.608v20.637 c0,11.54-9.356,20.896-20.896,20.896H104.879c-0.025,0-0.05,0-0.077,0c-2.708-0.01-4.56,2.742-3.368,5.174 C164.328,375.641,347.639,375.717,410.567,247.156z">
                                        </path>
                                        <path
                                            d="M400.157,221.085c3.846,0,6.965-3.119,6.965-6.965v-76.341c0-3.846-3.119-6.965-6.965-6.965h-98.184 c-3.846,0-6.965,3.119-6.965,6.965v76.341c0,3.846,3.119,6.965,6.965,6.965H400.157z">
                                        </path>
                                        <path
                                            d="M210.029,221.085c3.846,0,6.965-3.119,6.965-6.965v-76.341c0-3.846-3.119-6.965-6.965-6.965h-98.184 c-3.846,0-6.965,3.119-6.965,6.965v76.341c0,3.846,3.119,6.965,6.965,6.965H210.029z">
                                        </path>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </svg>
                    <span class="ms-3">
                        Professions
                    </span>
                </a>
            </li>

            <li>
                <a href="{{ route('admin.avis') }}"
                    class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                    <svg class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white mr-2"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z">
                        </path>
                    </svg>
                    <span class="ms-3">
                        Listes Avis
                    </span>
                </a>
            </li>

            <li>
                <a href="{{ route('admin.packages.show') }}"
                    class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                    <svg class="w-5 h-5 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd"
                            d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z"
                            clip-rule="evenodd"></path>
                    </svg>
                    <span class="ms-3">
                        Liste des packages
                    </span>
                </a>
            </li>

            <li>
                <a href="{{ route('admin.stripe-product.index') }}"
                    class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                    <svg fill="currentColor"
                        class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path
                            d="M12 5c.552 0 1 .448 1 1v5h5c.552 0 1 .448 1 1s-.448 1-1 1h-5v5c0 .552-.448 1-1 1s-1-.448-1-1v-5H6c-.552 0-1-.448-1-1s.448-1 1-1h5V6c0-.552.448-1 1-1z" />
                    </svg>
                    <span class="ms-3">
                        Créer le produit Stripe
                    </span>
                </a>
            </li>
            <li>
                <a href="{{ route('stripe-webhook.list') }}"
                    class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                    <svg fill="currentColor"
                        class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path
                            d="M12 2C9.243 2 7 4.243 7 7v7H4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-5a2 2 0 0 0-2-2h-3V7c0-2.757-2.243-5-5-5zM5 14h14v4H5v-4z" />
                    </svg>
                    <span class="ms-3">
                        Créer Webhook
                    </span>
                </a>
            </li>

            <!-- Nouveau menu avec sous-menus -->
            <li>
                <button type="button"
                    class="flex items-center w-full p-2 text-base text-gray-900 transition duration-75 rounded-lg group hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700"
                    aria-controls="dropdown-parametres" data-collapse-toggle="dropdown-parametres">
                    <svg fill="currentColor"
                        class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path
                            d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z" />
                        <path
                            d="M12 10c-1.103 0-2 .897-2 2s.897 2 2 2 2-.897 2-2-.897-2-2-2zm0 6c-2.206 0-4-1.794-4-4s1.794-4 4-4 4 1.794 4 4-1.794 4-4 4z" />
                        <path
                            d="M12 6c-3.309 0-6 2.691-6 6s2.691 6 6 6 6-2.691 6-6-2.691-6-6-6zm0 10c-2.206 0-4-1.794-4-4s1.794-4 4-4 4 1.794 4 4-1.794 4-4 4z" />
                    </svg>
                    <span class="flex-1 ms-3 text-left rtl:text-right whitespace-nowrap">Configuration Global de
                        l'application</span>

                </button>
                <ul id="dropdown-parametres" class="hidden py-2 space-y-2" style="margin-left: 1rem;">
                    <li>
                        <a href="{{ route('stripe-webhook.date-publication') }}"
                            class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                            <svg fill="currentColor"
                                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path
                                    d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z" />
                                <path d="M13 7h-2v6h6v-2h-4z" />
                            </svg>
                            <span class="ms-3">Période d’essai gratuite</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('stripe-webhook.parametre-tax') }}"
                            class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                            <svg fill="currentColor"
                                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <!-- Nouveau code SVG pour une icône de rouage -->
                                <path
                                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2V7zm0 8h2v2h-2v-2z" />
                            </svg>
                            <span class="ms-3">Parametre de taxe</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('stripe-webhook.stripe-api-key') }}"
                            class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                            <svg fill="none" stroke="currentColor"
                                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <!-- Icône de clé -->
                                <path
                                    d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"
                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <span class="ms-3">Clé API Stripe</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('parametre-smtp') }}"
                            class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                            <svg fill="currentColor"
                                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <!-- Icône d'enveloppe avec remplissage -->
                                <path
                                    d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
                            </svg>
                            <span class="ms-3">Paramètres SMTP</span>
                        </a>
                        </a>
                    </li>

                    <li>
                        <a href="{{ route('parametre-captcha') }}"
                            class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                            <svg fill="currentColor"
                                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <rect x="4" y="4" width="16" height="16" />
                            </svg>
                            <span class="ms-3">Paramètres Captcha</span>
                        </a>
                        </a>
                    </li>

                    <li>
                        <a href="{{ route('admin-mail') }}"
                            class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
                            <svg fill="currentColor"
                                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path
                                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                            </svg>
                            <span class="ms-3">Paramètres Admin mail</span>
                        </a>
                        </a>
                    </li>

                </ul>
            </li>

        </ul>
    </div>
</div>
