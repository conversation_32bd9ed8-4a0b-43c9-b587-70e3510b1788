<?php

namespace Database\Factories;

use App\Models\TypeProfession;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TypeProfession>
 */
class TypeProfessionFactory extends Factory
{
    protected $model = TypeProfession::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->randomElement(['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship']);
        return [
            'name' => $name,
            'slug' => \Illuminate\Support\Str::slug($name),
        ];
    }

    /**
     * Create full-time type.
     *
     * @return $this
     */
    public function fullTime(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Full-time',
            'slug' => 'full-time',
        ]);
    }

    /**
     * Create part-time type.
     *
     * @return $this
     */
    public function partTime(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Part-time',
            'slug' => 'part-time',
        ]);
    }
}
