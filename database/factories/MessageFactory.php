<?php

namespace Database\Factories;

use App\Models\Message;
use App\Models\Conversation;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Message>
 */
class MessageFactory extends Factory
{
    protected $model = Message::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'conversation_id' => Conversation::factory(),
            'sender_id' => User::factory(),
            'content' => fake()->paragraph(),
            'is_read' => fake()->boolean(),
        ];
    }

    /**
     * Create an unread message.
     *
     * @return $this
     */
    public function unread(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_read' => false,
        ]);
    }

    /**
     * Create a read message.
     *
     * @return $this
     */
    public function read(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_read' => true,
        ]);
    }

    /**
     * Create a message from a specific sender.
     *
     * @param string $senderId
     * @return $this
     */
    public function fromSender(string $senderId): static
    {
        return $this->state(fn (array $attributes) => [
            'sender_id' => $senderId,
        ]);
    }

    /**
     * Create a message in a specific conversation.
     *
     * @param string $conversationId
     * @return $this
     */
    public function inConversation(string $conversationId): static
    {
        return $this->state(fn (array $attributes) => [
            'conversation_id' => $conversationId,
        ]);
    }
}
