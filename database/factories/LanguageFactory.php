<?php

namespace Database\Factories;

use App\Models\Language;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Language>
 */
class LanguageFactory extends Factory
{
    protected $model = Language::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $languages = [
            ['name' => 'French', 'code' => 'fr'],
            ['name' => 'English', 'code' => 'en'],
            ['name' => 'German', 'code' => 'de'],
            ['name' => 'Italian', 'code' => 'it'],
            ['name' => 'Spanish', 'code' => 'es'],
            ['name' => 'Portuguese', 'code' => 'pt'],
            ['name' => 'Dutch', 'code' => 'nl'],
            ['name' => 'Russian', 'code' => 'ru'],
            ['name' => 'Chinese', 'code' => 'zh'],
            ['name' => 'Japanese', 'code' => 'ja'],
            ['name' => 'Arabic', 'code' => 'ar'],
            ['name' => 'Hindi', 'code' => 'hi'],
        ];

        $language = fake()->randomElement($languages);

        return [
            'name' => $language['name'],
            'code' => $language['code'],
            'is_active' => fake()->boolean(90), // 90% chance of being active
        ];
    }

    /**
     * Create a French language.
     *
     * @return $this
     */
    public function french(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'French',
            'code' => 'fr',
            'is_active' => true,
        ]);
    }

    /**
     * Create an English language.
     *
     * @return $this
     */
    public function english(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'English',
            'code' => 'en',
            'is_active' => true,
        ]);
    }

    /**
     * Create a German language.
     *
     * @return $this
     */
    public function german(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'German',
            'code' => 'de',
            'is_active' => true,
        ]);
    }

    /**
     * Create an active language.
     *
     * @return $this
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Create an inactive language.
     *
     * @return $this
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
