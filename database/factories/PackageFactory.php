<?php

namespace Database\Factories;

use App\Models\Package;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Package>
 */
class PackageFactory extends Factory
{
    protected $model = Package::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->words(2, true),
            'price' => fake()->randomFloat(2, 10, 500),
            'currency' => 'EUR',
            'tax_included' => fake()->boolean(),
            'features' => [
                fake()->sentence(),
                fake()->sentence(),
                fake()->sentence(),
            ],
        ];
    }

    /**
     * Create a basic package.
     *
     * @return $this
     */
    public function basic(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Package Basique',
            'price' => 29.99,
            'features' => [
                'Accès aux profils candidats',
                'Messagerie limitée',
                'Support par email',
            ],
        ]);
    }

    /**
     * Create a premium package.
     *
     * @return $this
     */
    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Package Premium',
            'price' => 99.99,
            'features' => [
                'Accès illimité aux profils',
                'Messagerie illimitée',
                'Recherche avancée',
                'Support prioritaire',
                'Téléchargement CV',
            ],
        ]);
    }

    /**
     * Create a free package.
     *
     * @return $this
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Package Gratuit',
            'price' => 0.00,
            'features' => [
                'Accès limité aux profils',
                'Messagerie restreinte',
            ],
        ]);
    }
}
