<?php

namespace Database\Factories;

use App\Models\Profession;
use App\Models\FieldActivity;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Profession>
 */
class ProfessionFactory extends Factory
{
    protected $model = Profession::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->jobTitle();
        return [
            'name' => $name,
            'slug' => \Illuminate\Support\Str::slug($name),
            'is_active' => true,
            'field_activity_id' => FieldActivity::factory(),
        ];
    }

    /**
     * Create developer profession.
     *
     * @return $this
     */
    public function developer(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Developer',
            'slug' => 'developer',
            'is_active' => true,
        ]);
    }

    /**
     * Create nurse profession.
     *
     * @return $this
     */
    public function nurse(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Nurse',
            'slug' => 'nurse',
            'is_active' => true,
        ]);
    }
}
