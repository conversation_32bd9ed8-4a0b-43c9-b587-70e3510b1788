<?php

namespace Database\Factories;

use App\Models\Formation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Formation>
 */
class FormationFactory extends Factory
{
    protected $model = Formation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $formations = [
            'Bachelor Degree in Computer Science',
            'Master Degree in Engineering',
            'PhD in Mathematics',
            'Certificate in Web Development',
            'Diploma in Nursing',
            'Bachelor Degree in Business Administration',
            'Master Degree in Marketing',
            'Certificate in Project Management',
            'Diploma in Graphic Design',
            'Bachelor Degree in Psychology',
            'Master Degree in Finance',
            'Certificate in Data Science',
            'Diploma in Culinary Arts',
            'Bachelor Degree in Medicine',
            'Master Degree in Architecture',
            'Certificate in Digital Marketing',
            'Diploma in Mechanical Engineering',
            'Bachelor Degree in Law',
            'Master Degree in Education',
            'Certificate in Cybersecurity',
        ];

        return [
            'name' => fake()->randomElement($formations),
            'description' => fake()->optional()->paragraph(),
            'duration_months' => fake()->numberBetween(6, 60),
            'level' => fake()->randomElement(['Certificate', 'Diploma', 'Bachelor', 'Master', 'PhD']),
            'is_active' => fake()->boolean(90),
        ];
    }

    /**
     * Create a bachelor degree formation.
     *
     * @return $this
     */
    public function bachelor(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Bachelor Degree in ' . fake()->randomElement(['Computer Science', 'Business', 'Engineering', 'Medicine']),
            'level' => 'Bachelor',
            'duration_months' => fake()->numberBetween(36, 48),
        ]);
    }

    /**
     * Create a master degree formation.
     *
     * @return $this
     */
    public function master(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Master Degree in ' . fake()->randomElement(['Engineering', 'Business Administration', 'Data Science', 'Marketing']),
            'level' => 'Master',
            'duration_months' => fake()->numberBetween(18, 24),
        ]);
    }

    /**
     * Create a certificate formation.
     *
     * @return $this
     */
    public function certificate(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Certificate in ' . fake()->randomElement(['Web Development', 'Project Management', 'Digital Marketing', 'Cybersecurity']),
            'level' => 'Certificate',
            'duration_months' => fake()->numberBetween(3, 12),
        ]);
    }

    /**
     * Create an active formation.
     *
     * @return $this
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Create an inactive formation.
     *
     * @return $this
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a short duration formation.
     *
     * @return $this
     */
    public function shortDuration(): static
    {
        return $this->state(fn (array $attributes) => [
            'duration_months' => fake()->numberBetween(1, 6),
        ]);
    }

    /**
     * Create a long duration formation.
     *
     * @return $this
     */
    public function longDuration(): static
    {
        return $this->state(fn (array $attributes) => [
            'duration_months' => fake()->numberBetween(48, 72),
        ]);
    }
}
