<?php

namespace Database\Factories;

use App\Models\ConfigGlobalApp;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ConfigGlobalApp>
 */
class ConfigGlobalAppFactory extends Factory
{
    protected $model = ConfigGlobalApp::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->word(),
            'value' => fake()->word(),
            'comment' => fake()->sentence(),
        ];
    }

    /**
     * Create a display_bandeau config.
     *
     * @return $this
     */
    public function displayBandeau(bool $enabled = true): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'display_bandeau',
            'value' => $enabled,
            'comment' => 'Display banner configuration',
        ]);
    }

    /**
     * Create a day_free_after_publish config.
     *
     * @return $this
     */
    public function dayFreeAfterPublish($date = null): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'day_free_after_publish',
            'value' => $date ?? now()->addDays(30)->toDateTimeString(),
            'comment' => 'Free trial expiration date',
        ]);
    }
}
