<?php

namespace Database\Factories;

use App\Models\Role;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Role>
 */
class RoleFactory extends Factory
{
    protected $model = Role::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->word(),
            'slug' => fake()->slug(),
        ];
    }

    /**
     * Create a candidate role.
     *
     * @return $this
     */
    public function candidate(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'candidate',
            'slug' => 'candidate',
        ]);
    }

    /**
     * Create a recruiter role.
     *
     * @return $this
     */
    public function recruiter(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'recruter',
            'slug' => 'recruter',
        ]);
    }

    /**
     * Create an admin role.
     *
     * @return $this
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'admin',
            'slug' => 'admin',
        ]);
    }
}
