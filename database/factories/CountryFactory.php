<?php

namespace Database\Factories;

use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Country>
 */
class CountryFactory extends Factory
{
    protected $model = Country::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->country(),
            'code' => fake()->countryCode(),
        ];
    }

    /**
     * Create Switzerland.
     *
     * @return $this
     */
    public function switzerland(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Switzerland',
            'code' => 'CH',
        ]);
    }

    /**
     * Create France.
     *
     * @return $this
     */
    public function france(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'France',
            'code' => 'FR',
        ]);
    }
}
