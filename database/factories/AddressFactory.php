<?php

namespace Database\Factories;

use App\Models\Address;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Address>
 */
class AddressFactory extends Factory
{
    protected $model = Address::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'name' => fake()->streetAddress(),
            'lat' => fake()->latitude(),
            'log' => fake()->longitude(),
        ];
    }

    /**
     * Create address in Geneva.
     *
     * @return $this
     */
    public function geneva(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Geneva, Switzerland',
            'lat' => 46.2044,
            'log' => 6.1432,
        ]);
    }

    /**
     * Create address in Zurich.
     *
     * @return $this
     */
    public function zurich(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Zurich, Switzerland',
            'lat' => 47.3769,
            'log' => 8.5417,
        ]);
    }
}
