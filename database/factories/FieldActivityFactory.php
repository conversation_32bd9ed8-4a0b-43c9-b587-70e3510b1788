<?php

namespace Database\Factories;

use App\Models\FieldActivity;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FieldActivity>
 */
class FieldActivityFactory extends Factory
{
    protected $model = FieldActivity::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->words(3, true);
        return [
            'name' => $name,
            'slug' => \Illuminate\Support\Str::slug($name),
            'is_active' => true,
        ];
    }

    /**
     * Create technology field.
     *
     * @return $this
     */
    public function technology(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Technology',
            'slug' => 'technology',
            'is_active' => true,
        ]);
    }

    /**
     * Create healthcare field.
     *
     * @return $this
     */
    public function healthcare(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Healthcare',
            'slug' => 'healthcare',
            'is_active' => true,
        ]);
    }
}
