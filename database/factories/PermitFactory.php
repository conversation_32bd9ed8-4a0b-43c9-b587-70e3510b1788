<?php

namespace Database\Factories;

use App\Models\Permit;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Permit>
 */
class PermitFactory extends Factory
{
    protected $model = Permit::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $permits = [
            ['name' => 'Driving License B', 'category' => 'driving', 'description' => 'Standard car driving license'],
            ['name' => 'Driving License C', 'category' => 'driving', 'description' => 'Truck driving license'],
            ['name' => 'Driving License D', 'category' => 'driving', 'description' => 'Bus driving license'],
            ['name' => 'Motorcycle License A', 'category' => 'driving', 'description' => 'Motorcycle driving license'],
            ['name' => 'Forklift License', 'category' => 'equipment', 'description' => 'Forklift operation permit'],
            ['name' => 'Crane Operator License', 'category' => 'equipment', 'description' => 'Crane operation permit'],
            ['name' => 'Welding Certificate', 'category' => 'professional', 'description' => 'Professional welding certification'],
            ['name' => 'Food Handler Permit', 'category' => 'professional', 'description' => 'Food safety handling permit'],
            ['name' => 'Security Guard License', 'category' => 'professional', 'description' => 'Security guard certification'],
            ['name' => 'First Aid Certificate', 'category' => 'safety', 'description' => 'Basic first aid certification'],
        ];

        $permit = fake()->randomElement($permits);

        return [
            'name' => $permit['name'],
            'category' => $permit['category'],
            'description' => $permit['description'],
            'is_active' => fake()->boolean(90),
            'requires_renewal' => fake()->boolean(60),
            'validity_months' => fake()->optional()->numberBetween(12, 60),
        ];
    }

    /**
     * Create a driving license permit.
     *
     * @return $this
     */
    public function drivingLicense(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Driving License ' . fake()->randomElement(['B', 'C', 'D']),
            'category' => 'driving',
            'description' => 'Vehicle driving license',
            'requires_renewal' => true,
            'validity_months' => 60,
        ]);
    }

    /**
     * Create a professional permit.
     *
     * @return $this
     */
    public function professional(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'professional',
            'requires_renewal' => fake()->boolean(80),
        ]);
    }

    /**
     * Create an equipment permit.
     *
     * @return $this
     */
    public function equipment(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'equipment',
            'requires_renewal' => true,
            'validity_months' => fake()->numberBetween(24, 36),
        ]);
    }

    /**
     * Create a safety permit.
     *
     * @return $this
     */
    public function safety(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'safety',
            'requires_renewal' => true,
            'validity_months' => fake()->numberBetween(12, 24),
        ]);
    }

    /**
     * Create an active permit.
     *
     * @return $this
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Create an inactive permit.
     *
     * @return $this
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a permit that requires renewal.
     *
     * @return $this
     */
    public function requiresRenewal(): static
    {
        return $this->state(fn (array $attributes) => [
            'requires_renewal' => true,
            'validity_months' => fake()->numberBetween(12, 60),
        ]);
    }

    /**
     * Create a permit that doesn't require renewal.
     *
     * @return $this
     */
    public function noRenewal(): static
    {
        return $this->state(fn (array $attributes) => [
            'requires_renewal' => false,
            'validity_months' => null,
        ]);
    }
}
