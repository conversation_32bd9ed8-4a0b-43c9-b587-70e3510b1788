<?php

namespace Database\Factories;

use App\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Plan>
 */
class PlanFactory extends Factory
{
    protected $model = Plan::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->words(2, true),
            'slug' => fake()->slug(),
            'stripe_product_id' => 'prod_' . fake()->regexify('[A-Za-z0-9]{14}'),
            'stripe_price_id' => 'price_' . fake()->regexify('[A-Za-z0-9]{14}'),
            'price' => fake()->randomFloat(2, 10, 500),
            'currency' => 'EUR',
            'show_name' => fake()->words(3, true),
            'description_html' => '<p>' . fake()->paragraph() . '</p>',
            'duration_in_days' => fake()->randomElement([30, 90, 365]),
        ];
    }

    /**
     * Create a monthly plan.
     *
     * @return $this
     */
    public function monthly(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Plan Mensuel',
            'slug' => 'monthly-plan',
            'duration_in_days' => 30,
            'price' => 29.99,
        ]);
    }

    /**
     * Create a yearly plan.
     *
     * @return $this
     */
    public function yearly(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Plan Annuel',
            'slug' => 'yearly-plan',
            'duration_in_days' => 365,
            'price' => 299.99,
        ]);
    }

    /**
     * Create a premium plan.
     *
     * @return $this
     */
    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Plan Premium',
            'slug' => 'premium-plan',
            'price' => 99.99,
            'show_name' => 'Premium - Accès Complet',
        ]);
    }
}
