<?php

namespace Database\Factories;

use App\Models\Role;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'remember_token' => Str::random(10),
            'is_suspend' => false,
            'role_id' => null, // Will be set by states
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     *
     * @return $this
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Create a candidate user.
     *
     * @return $this
     */
    public function candidate(): static
    {
        return $this->state(function (array $attributes) {
            $role = Role::where('slug', 'candidate')->first();
            return [
                'role_id' => $role ? $role->id : null,
            ];
        });
    }

    /**
     * Create a recruiter user.
     *
     * @return $this
     */
    public function recruiter(): static
    {
        return $this->state(function (array $attributes) {
            $role = Role::where('slug', 'recruter')->first();
            return [
                'role_id' => $role ? $role->id : null,
            ];
        });
    }

    /**
     * Create an admin user.
     *
     * @return $this
     */
    public function admin(): static
    {
        return $this->state(function (array $attributes) {
            $role = Role::where('slug', 'admin')->first();
            return [
                'role_id' => $role ? $role->id : null,
            ];
        });
    }

    /**
     * Create a suspended user.
     *
     * @return $this
     */
    public function suspended(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_suspend' => true,
        ]);
    }
}
