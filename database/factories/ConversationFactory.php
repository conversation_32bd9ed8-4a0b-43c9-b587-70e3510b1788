<?php

namespace Database\Factories;

use App\Models\Conversation;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Conversation>
 */
class ConversationFactory extends Factory
{
    protected $model = Conversation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_one_id' => User::factory(),
            'user_two_id' => User::factory(),
        ];
    }

    /**
     * Create a conversation between specific users.
     *
     * @param string $userOneId
     * @param string $userTwoId
     * @return $this
     */
    public function betweenUsers(string $userOneId, string $userTwoId): static
    {
        return $this->state(fn (array $attributes) => [
            'user_one_id' => $userOneId,
            'user_two_id' => $userTwoId,
        ]);
    }
}
