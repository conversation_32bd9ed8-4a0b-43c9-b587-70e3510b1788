<?php

namespace Database\Factories;

use App\Models\UserProfession;
use App\Models\User;
use App\Models\Profession;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserProfession>
 */
class UserProfessionFactory extends Factory
{
    protected $model = UserProfession::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'profession_id' => Profession::factory(),
            'experience_years' => fake()->numberBetween(0, 20),
            'skill_level' => fake()->randomElement(['beginner', 'intermediate', 'advanced', 'expert']),
            'is_primary' => fake()->boolean(30), // 30% chance of being primary profession
            'description' => fake()->optional()->paragraph(),
        ];
    }

    /**
     * Create a primary profession.
     *
     * @return $this
     */
    public function primary(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_primary' => true,
            'experience_years' => fake()->numberBetween(2, 15),
            'skill_level' => fake()->randomElement(['intermediate', 'advanced', 'expert']),
        ]);
    }

    /**
     * Create a secondary profession.
     *
     * @return $this
     */
    public function secondary(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_primary' => false,
            'experience_years' => fake()->numberBetween(0, 8),
        ]);
    }

    /**
     * Create a beginner level profession.
     *
     * @return $this
     */
    public function beginner(): static
    {
        return $this->state(fn (array $attributes) => [
            'skill_level' => 'beginner',
            'experience_years' => fake()->numberBetween(0, 2),
        ]);
    }

    /**
     * Create an expert level profession.
     *
     * @return $this
     */
    public function expert(): static
    {
        return $this->state(fn (array $attributes) => [
            'skill_level' => 'expert',
            'experience_years' => fake()->numberBetween(10, 20),
        ]);
    }
}
