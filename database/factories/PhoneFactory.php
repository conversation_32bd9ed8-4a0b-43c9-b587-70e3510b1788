<?php

namespace Database\Factories;

use App\Models\Phone;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Phone>
 */
class PhoneFactory extends Factory
{
    protected $model = Phone::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'number' => fake()->phoneNumber(),
        ];
    }

    /**
     * Create Swiss phone number.
     *
     * @return $this
     */
    public function swiss(): static
    {
        return $this->state(fn (array $attributes) => [
            'number' => '+41' . fake()->numerify('#########'),
        ]);
    }

    /**
     * Create French phone number.
     *
     * @return $this
     */
    public function french(): static
    {
        return $this->state(fn (array $attributes) => [
            'number' => '+33' . fake()->numerify('#########'),
        ]);
    }
}
