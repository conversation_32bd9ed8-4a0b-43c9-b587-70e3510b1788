<?php

namespace Database\Factories;

use App\Models\Subscription;
use App\Models\User;
use App\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Subscription>
 */
class SubscriptionFactory extends Factory
{
    protected $model = Subscription::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'plan_id' => Plan::factory(),
            'stripe_id' => 'sub_' . fake()->regexify('[A-Za-z0-9]{14}'),
            'stripe_status' => fake()->randomElement(['active', 'canceled', 'incomplete', 'past_due']),
            'quantity' => 1,
            'trial_ends_at' => null,
            'ends_at' => null,
        ];
    }

    /**
     * Create an active subscription.
     *
     * @return $this
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'stripe_status' => 'active',
            'ends_at' => null,
        ]);
    }

    /**
     * Create a canceled subscription.
     *
     * @return $this
     */
    public function canceled(): static
    {
        return $this->state(fn (array $attributes) => [
            'stripe_status' => 'canceled',
            'ends_at' => now()->addDays(30),
        ]);
    }

    /**
     * Create a subscription with trial.
     *
     * @return $this
     */
    public function withTrial(): static
    {
        return $this->state(fn (array $attributes) => [
            'trial_ends_at' => now()->addDays(14),
        ]);
    }

    /**
     * Create an expired subscription.
     *
     * @return $this
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'stripe_status' => 'canceled',
            'ends_at' => now()->subDays(1),
        ]);
    }
}
