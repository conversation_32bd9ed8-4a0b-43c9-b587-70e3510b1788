<?php

namespace Database\Factories;

use App\Models\Civility;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Civility>
 */
class CivilityFactory extends Factory
{
    protected $model = Civility::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'sexe' => fake()->randomElement(['male', 'female']),
            'date_of_birth' => fake()->date('Y-m-d', '-18 years'),
            'category' => fake()->randomElement(['current_profiles', 'retired', 'migrants', 'students']),
            'vehicle' => fake()->randomElement(['yes', 'no']),
            'criminal_record' => fake()->randomElement(['yes', 'no', 'skip']),
            'commune' => fake()->city(),
            'open_professions' => fake()->randomElement(['yes', 'no']),
            'contract_type' => fake()->randomElements(['cdi', 'cdd', 'call'], rand(1, 3)),
            'work_rate' => fake()->numberBetween(20, 100),
            'visibility' => fake()->randomElement(['0', '1']),
        ];
    }

    /**
     * Create a civility for a candidate.
     *
     * @return $this
     */
    public function forCandidate(): static
    {
        return $this->state(fn (array $attributes) => [
            'company_name' => null,
            'website' => null,
        ]);
    }

    /**
     * Create a civility for a recruiter.
     *
     * @return $this
     */
    public function forRecruiter(): static
    {
        return $this->state(fn (array $attributes) => [
            'company_name' => fake()->company(),
            'website' => fake()->url(),
            'first_name' => null,
            'last_name' => null,
        ]);
    }

    /**
     * Create a visible profile.
     *
     * @return $this
     */
    public function visible(): static
    {
        return $this->state(fn (array $attributes) => [
            'visibility' => '1',
        ]);
    }

    /**
     * Create a hidden profile.
     *
     * @return $this
     */
    public function hidden(): static
    {
        return $this->state(fn (array $attributes) => [
            'visibility' => '0',
        ]);
    }
}
