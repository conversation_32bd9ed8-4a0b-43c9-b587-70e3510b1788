<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use App\Models\Civility;
use App\Models\Phone;
use App\Models\Address;
use App\Models\Country;
use App\Models\FieldActivity;
use App\Models\Profession;
use App\Models\Language;
use App\Models\Formation;
use App\Models\Permit;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\UserProfession;
use App\Models\UserFieldActivity;
use Illuminate\Support\Facades\Hash;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $this->createRoles();
        
        // Create reference data
        $this->createReferenceData();
        
        // Create plans
        $this->createPlans();
        
        // Create test users
        $this->createTestUsers();
        
        // Create conversations and messages
        $this->createConversations();
    }

    /**
     * Create basic roles.
     */
    private function createRoles(): void
    {
        Role::create(['name' => 'Admin', 'slug' => 'admin']);
        Role::create(['name' => 'Candidate', 'slug' => 'candidate']);
        Role::create(['name' => 'Recruiter', 'slug' => 'recruter']);
    }

    /**
     * Create reference data.
     */
    private function createReferenceData(): void
    {
        // Countries
        $countries = [
            ['name' => 'Switzerland', 'code' => 'CH'],
            ['name' => 'France', 'code' => 'FR'],
            ['name' => 'Germany', 'code' => 'DE'],
            ['name' => 'Italy', 'code' => 'IT'],
            ['name' => 'Austria', 'code' => 'AT'],
        ];

        foreach ($countries as $country) {
            Country::create($country);
        }

        // Languages
        $languages = [
            ['name' => 'French', 'code' => 'fr'],
            ['name' => 'English', 'code' => 'en'],
            ['name' => 'German', 'code' => 'de'],
            ['name' => 'Italian', 'code' => 'it'],
            ['name' => 'Spanish', 'code' => 'es'],
        ];

        foreach ($languages as $language) {
            Language::create($language);
        }

        // Field Activities
        $fieldActivities = [
            'Information Technology',
            'Healthcare',
            'Finance',
            'Education',
            'Manufacturing',
            'Retail',
            'Construction',
            'Transportation',
            'Hospitality',
            'Legal Services',
        ];

        foreach ($fieldActivities as $activity) {
            FieldActivity::create(['name' => $activity]);
        }

        // Professions
        $professions = [
            'Software Developer',
            'Nurse',
            'Teacher',
            'Accountant',
            'Sales Manager',
            'Project Manager',
            'Marketing Specialist',
            'Data Analyst',
            'Customer Service Representative',
            'Operations Manager',
        ];

        foreach ($professions as $profession) {
            Profession::create(['name' => $profession]);
        }

        // Formations
        $formations = [
            'Bachelor Degree in Computer Science',
            'Master Degree in Business Administration',
            'Certificate in Project Management',
            'Diploma in Nursing',
            'Bachelor Degree in Engineering',
        ];

        foreach ($formations as $formation) {
            Formation::create(['name' => $formation]);
        }

        // Permits
        $permits = [
            'Driving License B',
            'Driving License C',
            'Forklift License',
            'First Aid Certificate',
            'Food Handler Permit',
        ];

        foreach ($permits as $permit) {
            Permit::create(['name' => $permit]);
        }
    }

    /**
     * Create subscription plans.
     */
    private function createPlans(): void
    {
        $plans = [
            [
                'name' => 'Basic Plan',
                'slug' => 'basic-plan',
                'stripe_product_id' => 'prod_basic',
                'stripe_price_id' => 'price_basic',
                'price' => 19.99,
                'currency' => 'CHF',
                'duration_in_days' => 30,
                'show_name' => 'Basic',
                'description_html' => '<p>Basic plan for small businesses</p>',
            ],
            [
                'name' => 'Premium Plan',
                'slug' => 'premium-plan',
                'stripe_product_id' => 'prod_premium',
                'stripe_price_id' => 'price_premium',
                'price' => 49.99,
                'currency' => 'CHF',
                'duration_in_days' => 30,
                'show_name' => 'Premium',
                'description_html' => '<p>Premium plan with advanced features</p>',
            ],
            [
                'name' => 'Enterprise Plan',
                'slug' => 'enterprise-plan',
                'stripe_product_id' => 'prod_enterprise',
                'stripe_price_id' => 'price_enterprise',
                'price' => 99.99,
                'currency' => 'CHF',
                'duration_in_days' => 30,
                'show_name' => 'Enterprise',
                'description_html' => '<p>Enterprise plan for large organizations</p>',
            ],
        ];

        foreach ($plans as $plan) {
            Plan::create($plan);
        }
    }

    /**
     * Create test users.
     */
    private function createTestUsers(): void
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        $adminRole = Role::where('slug', 'admin')->first();

        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $adminRole->id,
            'email_verified_at' => now(),
        ]);

        // Create test candidates
        for ($i = 1; $i <= 10; $i++) {
            $candidate = User::create([
                'name' => "Candidate {$i}",
                'email' => "candidate{$i}@example.com",
                'password' => Hash::make('password123'),
                'role_id' => $candidateRole->id,
                'email_verified_at' => now(),
            ]);

            // Create civility for candidate
            Civility::create([
                'user_id' => $candidate->id,
                'first_name' => "Candidate",
                'last_name' => "User {$i}",
                'sexe' => fake()->randomElement(['male', 'female']),
                'date_of_birth' => fake()->date('Y-m-d', '2000-01-01'),
                'category' => fake()->randomElement(['current_profiles', 'retired', 'migrants', 'students']),
                'visibility' => fake()->boolean(80) ? '1' : '0',
            ]);

            // Create phone for candidate
            Phone::create([
                'user_id' => $candidate->id,
                'phone' => fake()->phoneNumber(),
            ]);

            // Create address for candidate
            Address::create([
                'user_id' => $candidate->id,
                'street' => fake()->streetAddress(),
                'city' => fake()->city(),
                'postal_code' => fake()->postcode(),
                'country_id' => Country::inRandomOrder()->first()->id,
            ]);

            // Create user professions
            $professions = Profession::inRandomOrder()->take(rand(1, 3))->get();
            foreach ($professions as $profession) {
                UserProfession::create([
                    'user_id' => $candidate->id,
                    'profession_id' => $profession->id,
                ]);
            }

            // Create user field activities
            $fieldActivities = FieldActivity::inRandomOrder()->take(rand(1, 2))->get();
            foreach ($fieldActivities as $activity) {
                UserFieldActivity::create([
                    'user_id' => $candidate->id,
                    'field_activity_id' => $activity->id,
                ]);
            }
        }

        // Create test recruiters
        for ($i = 1; $i <= 5; $i++) {
            $recruiter = User::create([
                'name' => "Recruiter {$i}",
                'email' => "recruiter{$i}@example.com",
                'password' => Hash::make('password123'),
                'role_id' => $recruiterRole->id,
                'email_verified_at' => now(),
            ]);

            // Create civility for recruiter
            Civility::create([
                'user_id' => $recruiter->id,
                'company_name' => "Company {$i}",
                'website' => "https://company{$i}.com",
            ]);

            // Create phone for recruiter
            Phone::create([
                'user_id' => $recruiter->id,
                'phone' => fake()->phoneNumber(),
            ]);

            // Create subscription for some recruiters
            if ($i <= 3) {
                $plan = Plan::inRandomOrder()->first();
                Subscription::create([
                    'user_id' => $recruiter->id,
                    'plan_id' => $plan->id,
                    'stripe_id' => "sub_test{$i}",
                    'stripe_status' => 'active',
                    'quantity' => 1,
                ]);
            }
        }
    }

    /**
     * Create test conversations and messages.
     */
    private function createConversations(): void
    {
        $candidates = User::whereHas('role', function ($query) {
            $query->where('slug', 'candidate');
        })->take(5)->get();

        $recruiters = User::whereHas('role', function ($query) {
            $query->where('slug', 'recruter');
        })->take(3)->get();

        foreach ($candidates as $candidate) {
            foreach ($recruiters as $recruiter) {
                if (rand(1, 100) <= 30) { // 30% chance of conversation
                    $conversation = Conversation::create([
                        'user_one_id' => $candidate->id,
                        'user_two_id' => $recruiter->id,
                    ]);

                    // Create some messages
                    for ($i = 0; $i < rand(1, 5); $i++) {
                        Message::create([
                            'conversation_id' => $conversation->id,
                            'sender_id' => rand(0, 1) ? $candidate->id : $recruiter->id,
                            'content' => fake()->paragraph(),
                            'is_read' => fake()->boolean(70),
                        ]);
                    }
                }
            }
        }
    }
}
