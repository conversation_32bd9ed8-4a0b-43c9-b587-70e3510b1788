#!/bin/bash

# Cyclone Placement - Test Runner Script
# This script runs all tests and generates coverage reports

set -e

echo "🚀 Starting Cyclone Placement Test Suite"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    print_error "Please run this script from the Laravel project root directory"
    exit 1
fi

# Check if vendor directory exists
if [ ! -d "vendor" ]; then
    print_error "Vendor directory not found. Please run 'composer install' first"
    exit 1
fi

# Create coverage directory if it doesn't exist
mkdir -p storage/coverage

print_status "Setting up test environment..."

# Copy environment file for testing
if [ ! -f ".env.testing" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env.testing
        print_status "Created .env.testing from .env.example"
    else
        print_warning ".env.testing not found and .env.example doesn't exist"
    fi
fi

# Clear and cache config for testing
print_status "Preparing Laravel for testing..."
php artisan config:clear --env=testing
php artisan cache:clear --env=testing
php artisan view:clear --env=testing

# Run database migrations for testing
print_status "Setting up test database..."
php artisan migrate:fresh --env=testing --force
php artisan db:seed --class=TestDataSeeder --env=testing --force

print_status "Running Unit Tests..."
echo "====================="

# Run unit tests with coverage
php artisan test tests/Unit --coverage-html=storage/coverage/unit --coverage-clover=storage/coverage/unit-clover.xml

if [ $? -eq 0 ]; then
    print_success "Unit tests passed!"
else
    print_error "Unit tests failed!"
    exit 1
fi

print_status "Running Feature Tests..."
echo "========================"

# Run feature tests with coverage
php artisan test tests/Feature --coverage-html=storage/coverage/feature --coverage-clover=storage/coverage/feature-clover.xml

if [ $? -eq 0 ]; then
    print_success "Feature tests passed!"
else
    print_error "Feature tests failed!"
    exit 1
fi

print_status "Running Complete Test Suite..."
echo "=============================="

# Run all tests with comprehensive coverage
php artisan test --coverage-html=storage/coverage/complete --coverage-clover=storage/coverage/complete-clover.xml --coverage-text

if [ $? -eq 0 ]; then
    print_success "All tests passed!"
else
    print_error "Some tests failed!"
    exit 1
fi

print_status "Generating Test Reports..."
echo "=========================="

# Generate test summary
php artisan test --coverage-text > storage/coverage/test-summary.txt

print_success "Test execution completed!"
echo ""
echo "📊 Coverage Reports Generated:"
echo "  - HTML Report: storage/coverage/complete/index.html"
echo "  - Clover XML: storage/coverage/complete-clover.xml"
echo "  - Text Summary: storage/coverage/test-summary.txt"
echo ""
echo "🔍 To view the HTML coverage report:"
echo "  Open storage/coverage/complete/index.html in your browser"
echo ""

# Check coverage percentage (basic check)
if [ -f "storage/coverage/test-summary.txt" ]; then
    coverage_line=$(grep -E "Lines:" storage/coverage/test-summary.txt | head -1)
    if [ ! -z "$coverage_line" ]; then
        echo "📈 Coverage Summary:"
        echo "  $coverage_line"
        
        # Extract percentage (basic regex)
        percentage=$(echo "$coverage_line" | grep -oE '[0-9]+\.[0-9]+%' | head -1)
        if [ ! -z "$percentage" ]; then
            numeric_percentage=$(echo "$percentage" | sed 's/%//')
            if (( $(echo "$numeric_percentage >= 90" | bc -l) )); then
                print_success "Excellent! Coverage is above 90%: $percentage"
            elif (( $(echo "$numeric_percentage >= 80" | bc -l) )); then
                print_warning "Good coverage but could be improved: $percentage"
            else
                print_warning "Coverage is below 80%: $percentage - Consider adding more tests"
            fi
        fi
    fi
fi

echo ""
print_success "🎉 Test suite execution completed successfully!"
