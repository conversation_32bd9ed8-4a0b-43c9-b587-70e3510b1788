<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>
    <coverage>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
        <exclude>
            <directory suffix=".php">./app/Console</directory>
            <directory suffix=".php">./app/Exceptions</directory>
            <file>./app/Helpers/FileHelper.php</file>
            <file>./app/Providers/RouteServiceProvider.php</file>
        </exclude>
        <report>
            <html outputDirectory="storage/coverage/html"/>
            <clover outputFile="storage/coverage/clover.xml"/>
            <text outputFile="storage/coverage/coverage.txt"/>
        </report>
    </coverage>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_CONNECTION" value="mongodb_testing"/>
        <env name="DB_TEST_HOST" value="127.0.0.1"/>
        <env name="DB_TEST_PORT" value="27017"/>
        <env name="DB_TEST_DATABASE" value="cyclone_placement_test"/>
        <env name="DB_TEST_USERNAME" value=""/>
        <env name="DB_TEST_PASSWORD" value=""/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
