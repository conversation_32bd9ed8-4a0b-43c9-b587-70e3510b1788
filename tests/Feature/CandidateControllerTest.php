<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Civility;
use App\Models\Phone;
use App\Models\Country;
use App\Models\FieldActivity;
use App\Models\Profession;
use App\Models\Language;
use App\Models\Formation;
use App\Models\Permit;
use App\Models\ResidencePermit;
use App\Models\ResponsibilityCandidate;
use App\Models\TypeProfession;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class CandidateControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createBasicRoles();
        $this->createBasicData();
    }

    /**
     * Create basic test data.
     */
    protected function createBasicData()
    {
        // Create countries
        Country::create(['name' => 'Switzerland', 'code' => 'CH']);
        Country::create(['name' => 'France', 'code' => 'FR']);

        // Create field activities
        FieldActivity::create(['name' => 'Information Technology']);
        FieldActivity::create(['name' => 'Healthcare']);

        // Create professions
        Profession::create(['name' => 'Software Developer']);
        Profession::create(['name' => 'Nurse']);

        // Create languages
        Language::create(['name' => 'French', 'code' => 'fr']);
        Language::create(['name' => 'English', 'code' => 'en']);

        // Create formations
        Formation::create(['name' => 'Bachelor Degree']);
        Formation::create(['name' => 'Master Degree']);

        // Create permits
        Permit::create(['name' => 'Driving License B']);
        Permit::create(['name' => 'Driving License C']);

        // Create residence permits
        ResidencePermit::create(['name' => 'Swiss Citizen']);
        ResidencePermit::create(['name' => 'EU Citizen']);

        // Create responsibilities
        ResponsibilityCandidate::create(['name' => 'Team Leader']);
        ResponsibilityCandidate::create(['name' => 'Project Manager']);

        // Create type professions
        TypeProfession::create(['name' => 'Full-time']);
        TypeProfession::create(['name' => 'Part-time']);
    }

    /**
     * Test candidate registration page loads.
     */
    public function test_candidate_registration_page_loads()
    {
        $response = $this->get('/inscription-candidat');

        $response->assertStatus(200);
        $response->assertViewIs('candidate.register');
    }

    /**
     * Test candidate registration with valid data.
     */
    public function test_candidate_registration_with_valid_data()
    {
        Storage::fake('public');

        $country = Country::first();
        $fieldActivity = FieldActivity::first();
        $profession = Profession::first();
        $language = Language::first();
        $formation = Formation::first();
        $permit = Permit::first();
        $residencePermit = ResidencePermit::first();
        $responsibility = ResponsibilityCandidate::first();
        $typeProfession = TypeProfession::first();

        $data = [
            'email' => $this->getUniqueEmail('candidate.register'),
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'category' => 'current_profiles',
            'phone' => '+41123456789',
            'sexe' => 'male',
            'vehicle' => 'yes',
            'permits' => [$permit->id],
            'residence_permit' => $residencePermit->id,
            'criminal_record' => 'no',
            'country_of_residence' => $country->id,
            'commune' => 'Geneva',
            'address' => '123 Main St',
            'latitude_longitude' => '46.2044,6.1432',
            'open_professions' => 'yes',
            'type_profession' => $typeProfession->id,
            'contract_type' => 'permanent',
            'responsibility_candidate' => $responsibility->id,
            'work_rate' => '100',
            'native_language' => [$language->id],
            'fluent_languages' => [$language->id],
            'intermediate_languages' => [],
            'basic_languages' => [],
            'field_activities' => [$fieldActivity->id],
            'professions' => [$profession->id],
            'formations' => [$formation->id],
            'profession_1' => 'Software Developer',
            'duration_1' => '5 years',
            'terms' => true,
        ];

        $response = $this->post('/inscription-candidat', $data);

        $response->assertRedirect('/email/verify');
        $this->assertDatabaseHas('users', [
            'email' => $data['email'],
        ]);
    }

    /**
     * Test candidate registration with invalid email.
     */
    public function test_candidate_registration_with_invalid_email()
    {
        $data = [
            'email' => 'invalid-email',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'category' => 'current_profiles',
            'phone' => '+41123456789',
            'terms' => true,
        ];

        $response = $this->post('/inscription-candidat', $data);

        $response->assertSessionHasErrors(['email']);
        $this->assertDatabaseMissing('users', [
            'email' => $data['email'],
        ]);
    }

    /**
     * Test candidate registration with duplicate email.
     */
    public function test_candidate_registration_with_duplicate_email()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $email = $this->getUniqueEmail('existing.candidate');
        
        // Create existing user
        User::create([
            'name' => 'Existing User',
            'email' => $email,
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $data = [
            'email' => $email,
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'category' => 'current_profiles',
            'phone' => '+41123456789',
            'terms' => true,
        ];

        $response = $this->post('/inscription-candidat', $data);

        $response->assertSessionHasErrors(['email']);
    }

    /**
     * Test candidate registration with password mismatch.
     */
    public function test_candidate_registration_with_password_mismatch()
    {
        $data = [
            'email' => $this->getUniqueEmail('password.mismatch'),
            'password' => 'password123',
            'password_confirmation' => 'different_password',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'category' => 'current_profiles',
            'phone' => '+41123456789',
            'terms' => true,
        ];

        $response = $this->post('/inscription-candidat', $data);

        $response->assertSessionHasErrors(['password']);
    }

    /**
     * Test candidate registration without accepting terms.
     */
    public function test_candidate_registration_without_accepting_terms()
    {
        $data = [
            'email' => $this->getUniqueEmail('no.terms'),
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'category' => 'current_profiles',
            'phone' => '+41123456789',
            // 'terms' => true, // Missing terms acceptance
        ];

        $response = $this->post('/inscription-candidat', $data);

        $response->assertSessionHasErrors(['terms']);
    }

    /**
     * Test candidate dashboard access requires authentication.
     */
    public function test_candidate_dashboard_requires_authentication()
    {
        $response = $this->get('/candidate/dashboard');

        $response->assertRedirect('/login');
    }

    /**
     * Test authenticated candidate can access dashboard.
     */
    public function test_authenticated_candidate_can_access_dashboard()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('dashboard.candidate'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);

        // Create civility for the candidate
        Civility::create([
            'user_id' => $candidate->id,
            'first_name' => 'John',
            'last_name' => 'Candidate',
        ]);

        $response = $this->actingAs($candidate)->get('/candidate/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('candidate.dashboard');
    }

    /**
     * Test candidate profile update with valid data.
     */
    public function test_candidate_profile_update_with_valid_data()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $country = Country::first();
        
        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('update.candidate'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);

        // Create civility for the candidate
        Civility::create([
            'user_id' => $candidate->id,
            'first_name' => 'John',
            'last_name' => 'Candidate',
        ]);

        $updateData = [
            'visibility' => '1',
            'first_name' => 'John Updated',
            'last_name' => 'Candidate Updated',
            'date_of_birth' => '1990-01-01',
            'category' => 'current_profiles',
            'phone' => '+41987654321',
            'vehicle' => 'no',
            'country_of_residence' => $country->id,
            'commune' => 'Zurich',
            'address' => '456 Updated St',
            'latitude_longitude' => '47.3769,8.5417',
        ];

        $response = $this->actingAs($candidate)
                         ->put('/candidate/dashboard', $updateData);

        $response->assertRedirect('/candidate/dashboard');
        $response->assertSessionHas('success');
        
        // Verify civility was updated
        $this->assertDatabaseHas('civilities', [
            'user_id' => $candidate->id,
            'first_name' => 'John Updated',
            'last_name' => 'Candidate Updated',
        ]);
    }
}
