<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\Plan;
use App\Models\Subscription;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class MessagingControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createBasicRoles();
        $this->createBasicData();
    }

    /**
     * Create basic test data.
     */
    protected function createBasicData()
    {
        // Create plan for subscriptions
        Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);
    }

    /**
     * Test conversation creation between candidate and recruiter.
     */
    public function test_conversation_creation_between_candidate_and_recruiter()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('candidate.conversation'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('recruiter.conversation'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create subscription for recruiter
        $plan = Plan::first();
        Subscription::create([
            'user_id' => $recruiter->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        $conversationData = [
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ];

        $response = $this->actingAs($recruiter)
                         ->post('/conversations', $conversationData);

        $response->assertStatus(201);
        $this->assertDatabaseHas('conversations', [
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);
    }

    /**
     * Test sending message in conversation.
     */
    public function test_sending_message_in_conversation()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('candidate.message'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('recruiter.message'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create subscription for recruiter
        $plan = Plan::first();
        Subscription::create([
            'user_id' => $recruiter->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        // Create conversation
        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $messageData = [
            'conversation_id' => $conversation->id,
            'content' => 'Hello, I am interested in this position.',
        ];

        $response = $this->actingAs($candidate)
                         ->post('/messages', $messageData);

        $response->assertStatus(201);
        $this->assertDatabaseHas('messages', [
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Hello, I am interested in this position.',
            'is_read' => false,
        ]);
    }

    /**
     * Test retrieving conversation messages.
     */
    public function test_retrieving_conversation_messages()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('candidate.retrieve'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('recruiter.retrieve'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create subscription for recruiter
        $plan = Plan::first();
        Subscription::create([
            'user_id' => $recruiter->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        // Create conversation
        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        // Create messages
        Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Hello, I am interested in this position.',
            'is_read' => false,
        ]);

        Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $recruiter->id,
            'content' => 'Thank you for your interest!',
            'is_read' => false,
        ]);

        $response = $this->actingAs($candidate)
                         ->get("/conversations/{$conversation->id}/messages");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'messages' => [
                '*' => [
                    'id',
                    'conversation_id',
                    'sender_id',
                    'content',
                    'is_read',
                    'created_at',
                    'updated_at',
                ]
            ]
        ]);

        $messages = $response->json('messages');
        $this->assertCount(2, $messages);
    }

    /**
     * Test marking message as read.
     */
    public function test_marking_message_as_read()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('candidate.read'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('recruiter.read'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create subscription for recruiter
        $plan = Plan::first();
        Subscription::create([
            'user_id' => $recruiter->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        // Create conversation
        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        // Create message
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Hello, I am interested in this position.',
            'is_read' => false,
        ]);

        $response = $this->actingAs($recruiter)
                         ->patch("/messages/{$message->id}/read");

        $response->assertStatus(200);
        $this->assertDatabaseHas('messages', [
            'id' => $message->id,
            'is_read' => true,
        ]);
    }

    /**
     * Test user can only access their own conversations.
     */
    public function test_user_can_only_access_their_own_conversations()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate1 = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('candidate1.access'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);
        
        $candidate2 = User::create([
            'name' => 'Jane Candidate',
            'email' => $this->getUniqueEmail('candidate2.access'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);
        
        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => $this->getUniqueEmail('recruiter.access'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create conversation between candidate1 and recruiter
        $conversation = Conversation::create([
            'user_one_id' => $candidate1->id,
            'user_two_id' => $recruiter->id,
        ]);

        // Candidate2 should not be able to access this conversation
        $response = $this->actingAs($candidate2)
                         ->get("/conversations/{$conversation->id}/messages");

        $response->assertStatus(403);
    }

    /**
     * Test conversation list for user.
     */
    public function test_conversation_list_for_user()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('candidate.list'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);
        
        $recruiter1 = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('recruiter1.list'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);
        
        $recruiter2 = User::create([
            'name' => 'Bob Recruiter',
            'email' => $this->getUniqueEmail('recruiter2.list'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create conversations
        $conversation1 = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter1->id,
        ]);

        $conversation2 = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter2->id,
        ]);

        $response = $this->actingAs($candidate)
                         ->get('/conversations');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'conversations' => [
                '*' => [
                    'id',
                    'user_one_id',
                    'user_two_id',
                    'created_at',
                    'updated_at',
                ]
            ]
        ]);

        $conversations = $response->json('conversations');
        $this->assertCount(2, $conversations);
    }

    /**
     * Test empty message cannot be sent.
     */
    public function test_empty_message_cannot_be_sent()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('candidate.empty'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('recruiter.empty'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create conversation
        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $messageData = [
            'conversation_id' => $conversation->id,
            'content' => '', // Empty content
        ];

        $response = $this->actingAs($candidate)
                         ->post('/messages', $messageData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['content']);
    }
}
