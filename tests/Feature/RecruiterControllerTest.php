<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Civility;
use App\Models\Phone;
use App\Models\Country;
use App\Models\Address;
use App\Models\Plan;
use App\Models\Subscription;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class RecruiterControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createBasicRoles();
        $this->createBasicData();
    }

    /**
     * Create basic test data.
     */
    protected function createBasicData()
    {
        // Create countries
        Country::create(['name' => 'Switzerland', 'code' => 'CH']);
        Country::create(['name' => 'France', 'code' => 'FR']);

        // Create plans
        Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);
    }

    /**
     * Test recruiter dashboard access requires authentication.
     */
    public function test_recruiter_dashboard_requires_authentication()
    {
        $response = $this->get('/recruter/dashboard');

        $response->assertRedirect('/login');
    }

    /**
     * Test authenticated recruiter can access dashboard.
     */
    public function test_authenticated_recruiter_can_access_dashboard()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('dashboard.recruiter'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create civility for the recruiter
        Civility::create([
            'user_id' => $recruiter->id,
            'company_name' => 'Tech Corp',
        ]);

        $response = $this->actingAs($recruiter)->get('/recruter/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('recruter.dashboard');
    }

    /**
     * Test recruiter profile update with valid data.
     */
    public function test_recruiter_profile_update_with_valid_data()
    {
        Storage::fake('public');
        
        $recruiterRole = Role::where('slug', 'recruter')->first();
        $country = Country::first();
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('update.recruiter'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create civility for the recruiter
        Civility::create([
            'user_id' => $recruiter->id,
            'company_name' => 'Tech Corp',
        ]);

        $updateData = [
            'company_name' => 'Updated Tech Corp',
            'phone' => '+***********',
            'website' => 'https://updated-tech-corp.com',
            'country_of_residence' => $country->id,
            'commune' => 'Zurich',
            'address' => '456 Business St',
            'latitude_longitude' => '47.3769,8.5417',
        ];

        $response = $this->actingAs($recruiter)
                         ->put('/recruter/dashboard', $updateData);

        $response->assertRedirect('/recruter/dashboard');
        $response->assertSessionHas('success');
        
        // Verify civility was updated
        $this->assertDatabaseHas('civilities', [
            'user_id' => $recruiter->id,
            'company_name' => 'Updated Tech Corp',
            'website' => 'https://updated-tech-corp.com',
        ]);
    }

    /**
     * Test recruiter profile update with invalid data.
     */
    public function test_recruiter_profile_update_with_invalid_data()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('invalid.recruiter'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        $updateData = [
            'company_name' => '', // Required field empty
            'phone' => 'invalid-phone',
            'latitude_longitude' => 'invalid-coordinates',
        ];

        $response = $this->actingAs($recruiter)
                         ->put('/recruter/dashboard', $updateData);

        $response->assertSessionHasErrors(['company_name', 'phone', 'latitude_longitude']);
    }

    /**
     * Test recruiter can view candidates list.
     */
    public function test_recruiter_can_view_candidates_list()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('list.recruiter'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create a subscribed recruiter
        $plan = Plan::first();
        Subscription::create([
            'user_id' => $recruiter->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        // Create visible candidates
        $candidate1 = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('candidate1'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);

        Civility::create([
            'user_id' => $candidate1->id,
            'first_name' => 'John',
            'last_name' => 'Candidate',
            'visibility' => '1', // Visible
        ]);

        $candidate2 = User::create([
            'name' => 'Jane Candidate',
            'email' => $this->getUniqueEmail('candidate2'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);

        Civility::create([
            'user_id' => $candidate2->id,
            'first_name' => 'Jane',
            'last_name' => 'Candidate',
            'visibility' => '0', // Not visible
        ]);

        $response = $this->actingAs($recruiter)->get('/recruter/list-candidate');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'candidates'
        ]);
        
        $candidates = $response->json('candidates');
        $this->assertCount(1, $candidates); // Only visible candidate
    }

    /**
     * Test recruiter can search candidates.
     */
    public function test_recruiter_can_search_candidates()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('search.recruiter'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create a subscribed recruiter
        $plan = Plan::first();
        Subscription::create([
            'user_id' => $recruiter->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        $response = $this->actingAs($recruiter)->get('/recruter/search-candidate');

        $response->assertStatus(200);
        $response->assertViewIs('recruter.search-candidate');
    }

    /**
     * Test recruiter packages page.
     */
    public function test_recruiter_packages_page()
    {
        $response = $this->get('/recruter/packages');

        $response->assertStatus(200);
        $response->assertViewIs('recruter.packages');
    }

    /**
     * Test recruiter subscription page requires authentication.
     */
    public function test_recruiter_subscription_page_requires_authentication()
    {
        $response = $this->get('/recruter/subscription');

        $response->assertRedirect('/login');
    }

    /**
     * Test authenticated recruiter can access subscription page.
     */
    public function test_authenticated_recruiter_can_access_subscription_page()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('subscription.recruiter'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($recruiter)->get('/recruter/subscription');

        $response->assertStatus(200);
        $response->assertViewIs('recruter.subscription');
    }

    /**
     * Test recruiter can generate candidate PDF.
     */
    public function test_recruiter_can_generate_candidate_pdf()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('pdf.recruiter'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        // Create a subscribed recruiter
        $plan = Plan::first();
        Subscription::create([
            'user_id' => $recruiter->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('pdf.candidate'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => now(),
        ]);

        Civility::create([
            'user_id' => $candidate->id,
            'first_name' => 'John',
            'last_name' => 'Candidate',
            'visibility' => '1',
        ]);

        $response = $this->actingAs($recruiter)
                         ->get("/recruter/generate-pdf/{$candidate->id}");

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/pdf');
    }

    /**
     * Test non-subscribed recruiter cannot access candidate list.
     */
    public function test_non_subscribed_recruiter_cannot_access_candidate_list()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('non.subscribed'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($recruiter)->get('/recruter/list-candidate');

        $response->assertRedirect('/recruter/packages');
    }
}
