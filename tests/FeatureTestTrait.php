<?php

namespace Tests;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

trait FeatureTestTrait
{
    use DatabaseTestTrait;

    /**
     * Setup for feature tests.
     */
    protected function setUpFeatureTest(): void
    {
        // Setup database
        $this->setUpDatabase();
        
        // Configure session for feature tests
        config(['session.driver' => 'array']);
        config(['session.connection' => null]);
        
        // Start session
        Session::start();
        
        // Disable CSRF for testing
        $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);
    }

    /**
     * Tear down feature test.
     */
    protected function tearDownFeatureTest(): void
    {
        $this->tearDownDatabase();
    }

    /**
     * Create a test user with specific role.
     */
    protected function createTestUser(string $role = 'candidate'): \App\Models\User
    {
        $this->createBasicRoles();
        
        $roleModel = DB::connection('mongodb_testing')->table('roles')
            ->where('slug', $role)
            ->first();

        return \App\Models\User::create([
            'name' => 'Test User',
            'email' => $this->getUniqueEmail('test.user'),
            'password' => \Illuminate\Support\Facades\Hash::make('password'),
            'role_id' => $roleModel->_id,
            'is_suspend' => false,
        ]);
    }
}
