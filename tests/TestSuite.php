<?php

namespace Tests;

use PHPUnit\Framework\TestSuite as PHPUnitTestSuite;

/**
 * Custom test suite for comprehensive testing.
 */
class TestSuite extends PHPUnitTestSuite
{
    /**
     * Create the test suite.
     */
    public static function suite(): self
    {
        $suite = new self('Cyclone Placement Test Suite');
        
        // Add all test directories
        $suite->addTestDirectory(__DIR__ . '/Unit');
        $suite->addTestDirectory(__DIR__ . '/Feature');
        
        return $suite;
    }

    /**
     * Add all tests from a directory recursively.
     */
    private function addTestDirectory(string $directory): void
    {
        if (!is_dir($directory)) {
            return;
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $this->addTestFile($file->getPathname());
            }
        }
    }
}
