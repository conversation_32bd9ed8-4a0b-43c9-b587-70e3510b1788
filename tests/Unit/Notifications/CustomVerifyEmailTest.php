<?php

namespace Tests\Unit\Notifications;

use Tests\TestCase;
use App\Notifications\CustomVerifyEmail;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;

class CustomVerifyEmailTest extends TestCase
{
    /**
     * Test CustomVerifyEmail notification creation.
     */
    public function test_custom_verify_email_notification_creation()
    {
        $fullname = '<PERSON>';
        $email = '<EMAIL>';
        $actionUrl = 'https://example.com/verify';
        $roleSlug = 'candidate';

        $notification = new CustomVerifyEmail($fullname, $email, $actionUrl, $roleSlug);

        $this->assertInstanceOf(CustomVerifyEmail::class, $notification);
    }

    /**
     * Test notification via method returns mail.
     */
    public function test_notification_via_method_returns_mail()
    {
        $notification = new CustomVerifyEmail('<PERSON> Doe', '<EMAIL>', 'https://example.com/verify', 'candidate');
        
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => '<PERSON> Doe',
            'email' => $this->getUniqueEmail('john.via'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $via = $notification->via($user);
        
        $this->assertIsArray($via);
        $this->assertContains('mail', $via);
        $this->assertCount(1, $via);
    }

    /**
     * Test notification toMail method structure.
     */
    public function test_notification_to_mail_method_structure()
    {
        Mail::fake();
        
        $fullname = 'John Doe';
        $email = '<EMAIL>';
        $actionUrl = 'https://example.com/verify';
        $roleSlug = 'candidate';

        $notification = new CustomVerifyEmail($fullname, $email, $actionUrl, $roleSlug);
        
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.mail'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $mailMessage = $notification->toMail($user);

        $this->assertInstanceOf(\Illuminate\Notifications\Messages\MailMessage::class, $mailMessage);
    }

    /**
     * Test notification sends copy to admin.
     */
    public function test_notification_sends_copy_to_admin()
    {
        Mail::fake();
        
        $fullname = 'John Doe';
        $email = '<EMAIL>';
        $actionUrl = 'https://example.com/verify';
        $roleSlug = 'candidate';

        $notification = new CustomVerifyEmail($fullname, $email, $actionUrl, $roleSlug);
        
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.admin'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $notification->toMail($user);

        // Verify that mail was sent to admin
        Mail::assertSent(function ($mail) {
            return $mail->hasTo('<EMAIL>');
        });
    }

    /**
     * Test notification with different role slugs.
     */
    public function test_notification_with_different_role_slugs()
    {
        Mail::fake();
        
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('john.candidate'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);
        
        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('jane.recruiter'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Test candidate notification
        $candidateNotification = new CustomVerifyEmail('John Candidate', $candidate->email, 'https://example.com/verify', 'candidate');
        $candidateMailMessage = $candidateNotification->toMail($candidate);
        $this->assertInstanceOf(\Illuminate\Notifications\Messages\MailMessage::class, $candidateMailMessage);

        // Test recruiter notification
        $recruiterNotification = new CustomVerifyEmail('Jane Recruiter', $recruiter->email, 'https://example.com/verify', 'recruter');
        $recruiterMailMessage = $recruiterNotification->toMail($recruiter);
        $this->assertInstanceOf(\Illuminate\Notifications\Messages\MailMessage::class, $recruiterMailMessage);
    }

    /**
     * Test notification with empty values.
     */
    public function test_notification_with_empty_values()
    {
        Mail::fake();
        
        $notification = new CustomVerifyEmail('', '', '', '');
        
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'Test User',
            'email' => $this->getUniqueEmail('test.empty'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $mailMessage = $notification->toMail($user);
        $this->assertInstanceOf(\Illuminate\Notifications\Messages\MailMessage::class, $mailMessage);
    }

    /**
     * Test notification with special characters in name.
     */
    public function test_notification_with_special_characters()
    {
        Mail::fake();
        
        $fullname = 'José María Ñoño';
        $email = '<EMAIL>';
        $actionUrl = 'https://example.com/verify?token=abc123';
        $roleSlug = 'candidate';

        $notification = new CustomVerifyEmail($fullname, $email, $actionUrl, $roleSlug);
        
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => $fullname,
            'email' => $this->getUniqueEmail('jose.special'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $mailMessage = $notification->toMail($user);
        $this->assertInstanceOf(\Illuminate\Notifications\Messages\MailMessage::class, $mailMessage);
    }

    /**
     * Test notification subject.
     */
    public function test_notification_subject()
    {
        Mail::fake();
        
        $notification = new CustomVerifyEmail('John Doe', '<EMAIL>', 'https://example.com/verify', 'candidate');
        
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.subject'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $mailMessage = $notification->toMail($user);
        
        // Access the subject through reflection since it's not directly accessible
        $reflection = new \ReflectionClass($mailMessage);
        $subjectProperty = $reflection->getProperty('subject');
        $subjectProperty->setAccessible(true);
        $subject = $subjectProperty->getValue($mailMessage);
        
        $this->assertEquals('Bienvenue sur Cyclone Placement', $subject);
    }

    /**
     * Test notification uses correct view.
     */
    public function test_notification_uses_correct_view()
    {
        Mail::fake();
        
        $notification = new CustomVerifyEmail('John Doe', '<EMAIL>', 'https://example.com/verify', 'candidate');
        
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.view'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $mailMessage = $notification->toMail($user);
        
        // Access the view through reflection
        $reflection = new \ReflectionClass($mailMessage);
        $viewProperty = $reflection->getProperty('view');
        $viewProperty->setAccessible(true);
        $view = $viewProperty->getValue($mailMessage);
        
        $this->assertEquals('vendor.notifications.verify-email', $view);
    }

    /**
     * Test notification with long action URL.
     */
    public function test_notification_with_long_action_url()
    {
        Mail::fake();
        
        $longUrl = 'https://example.com/verify?token=' . str_repeat('a', 500) . '&expires=' . time();
        $notification = new CustomVerifyEmail('John Doe', '<EMAIL>', $longUrl, 'candidate');
        
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.long'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $mailMessage = $notification->toMail($user);
        $this->assertInstanceOf(\Illuminate\Notifications\Messages\MailMessage::class, $mailMessage);
    }
}
