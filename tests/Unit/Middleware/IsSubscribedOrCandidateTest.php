<?php

namespace Tests\Unit\Middleware;

use Tests\TestCase;
use App\Http\Middleware\IsSubscribedOrCandidate;
use App\Models\User;
use App\Models\Role;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\ConfigGlobalApp;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class IsSubscribedOrCandidateTest extends TestCase
{
    protected $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new IsSubscribedOrCandidate();
        $this->createBasicRoles();
    }

    /**
     * Test middleware allows access for candidates.
     */
    public function test_middleware_allows_access_for_candidates()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    /**
     * Test middleware allows access for subscribed recruiters.
     */
    public function test_middleware_allows_access_for_subscribed_recruiters()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    /**
     * Test middleware allows access for recruiters during free trial.
     */
    public function test_middleware_allows_access_for_recruiters_during_free_trial()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Enable banner display
        ConfigGlobalApp::create([
            'name' => 'display_bandeau',
            'value' => true,
            'comment' => 'Display banner configuration',
        ]);

        // Set trial expiration to future date
        ConfigGlobalApp::create([
            'name' => 'day_free_after_publish',
            'value' => now()->addDays(15)->toDateTimeString(),
            'comment' => 'Free trial expiration date',
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    /**
     * Test middleware redirects unsubscribed recruiters to packages page.
     */
    public function test_middleware_redirects_unsubscribed_recruiters_to_packages_page()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/packages', $response->headers->get('Location'));
    }

    /**
     * Test middleware redirects recruiters when trial has expired.
     */
    public function test_middleware_redirects_recruiters_when_trial_has_expired()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Enable banner display
        ConfigGlobalApp::create([
            'name' => 'display_bandeau',
            'value' => true,
            'comment' => 'Display banner configuration',
        ]);

        // Set trial expiration to past date
        ConfigGlobalApp::create([
            'name' => 'day_free_after_publish',
            'value' => now()->subDays(5)->toDateTimeString(),
            'comment' => 'Free trial expiration date',
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/packages', $response->headers->get('Location'));
    }

    /**
     * Test middleware with admin user.
     */
    public function test_middleware_with_admin_user()
    {
        $adminRole = Role::where('slug', 'admin')->first();
        
        $user = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $adminRole->id,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        // Admin should be redirected to packages since they're not candidate or subscribed recruiter
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/packages', $response->headers->get('Location'));
    }

    /**
     * Test middleware with recruiter having canceled subscription.
     */
    public function test_middleware_with_recruiter_having_canceled_subscription()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'canceled',
            'quantity' => 1,
            'ends_at' => now()->addDays(30),
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/packages', $response->headers->get('Location'));
    }

    /**
     * Test middleware with multiple candidates.
     */
    public function test_middleware_with_multiple_candidates()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $candidate1 = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $candidate2 = User::create([
            'name' => 'John Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Test first candidate
        Auth::login($candidate1);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response->getStatusCode());

        // Test second candidate
        Auth::login($candidate2);

        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * Test middleware with mixed user types in sequence.
     */
    public function test_middleware_with_mixed_user_types_in_sequence()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $request = Request::create('/test', 'GET');

        // Test candidate access
        Auth::login($candidate);
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response->getStatusCode());

        // Test unsubscribed recruiter access
        Auth::login($recruiter);
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/packages', $response->headers->get('Location'));
    }
}
