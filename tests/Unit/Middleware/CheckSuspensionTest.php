<?php

namespace Tests\Unit\Middleware;

use Tests\TestCase;
use App\Http\Middleware\CheckSuspension;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class CheckSuspensionTest extends TestCase
{
    protected $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new CheckSuspension();
        $this->createBasicRoles();
    }

    /**
     * Test middleware allows access for non-suspended users.
     */
    public function test_middleware_allows_access_for_non_suspended_users()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'is_suspend' => false,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    /**
     * Test middleware allows access for unauthenticated users.
     */
    public function test_middleware_allows_access_for_unauthenticated_users()
    {
        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    /**
     * Test middleware redirects suspended users to suspension page.
     */
    public function test_middleware_redirects_suspended_users_to_suspension_page()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'Suspended User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'is_suspend' => true,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('suspenssion-page', $response->headers->get('Location'));
    }

    /**
     * Test middleware logs out suspended users.
     */
    public function test_middleware_logs_out_suspended_users()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'Suspended User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'is_suspend' => true,
        ]);

        Auth::login($user);
        
        // Verify user is logged in before middleware
        $this->assertTrue(Auth::check());
        $this->assertEquals($user->id, Auth::id());

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        // Verify user is logged out after middleware
        $this->assertFalse(Auth::check());
        $this->assertNull(Auth::user());
    }

    /**
     * Test middleware with candidate suspended user.
     */
    public function test_middleware_with_candidate_suspended_user()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'Suspended Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'is_suspend' => true,
        ]);

        Auth::login($user);

        $request = Request::create('/candidats/dashboard', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Dashboard');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('suspenssion-page', $response->headers->get('Location'));
        $this->assertFalse(Auth::check());
    }

    /**
     * Test middleware with recruiter suspended user.
     */
    public function test_middleware_with_recruiter_suspended_user()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Suspended Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
            'is_suspend' => true,
        ]);

        Auth::login($user);

        $request = Request::create('/recruter/dashboard', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Dashboard');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('suspenssion-page', $response->headers->get('Location'));
        $this->assertFalse(Auth::check());
    }

    /**
     * Test middleware with admin suspended user.
     */
    public function test_middleware_with_admin_suspended_user()
    {
        $adminRole = Role::where('slug', 'admin')->first();
        
        $user = User::create([
            'name' => 'Suspended Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $adminRole->id,
            'is_suspend' => true,
        ]);

        Auth::login($user);

        $request = Request::create('/admin', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Admin Dashboard');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('suspenssion-page', $response->headers->get('Location'));
        $this->assertFalse(Auth::check());
    }

    /**
     * Test middleware with user that becomes suspended during session.
     */
    public function test_middleware_with_user_suspended_during_session()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'User to Suspend',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'is_suspend' => false,
        ]);

        Auth::login($user);

        // First request should work
        $request1 = Request::create('/test', 'GET');
        
        $response1 = $this->middleware->handle($request1, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response1->getStatusCode());
        $this->assertTrue(Auth::check());

        // Suspend the user
        $user->update(['is_suspend' => true]);

        // Second request should redirect and log out
        $request2 = Request::create('/test', 'GET');
        
        $response2 = $this->middleware->handle($request2, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response2->getStatusCode());
        $this->assertStringContainsString('suspenssion-page', $response2->headers->get('Location'));
        $this->assertFalse(Auth::check());
    }

    /**
     * Test middleware with null is_suspend value (should be treated as not suspended).
     */
    public function test_middleware_with_null_is_suspend_value()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'User with Null Suspend',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            // is_suspend is not set, should default to null/false
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
        $this->assertTrue(Auth::check());
    }
}
