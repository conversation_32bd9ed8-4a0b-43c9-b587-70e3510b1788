<?php

namespace Tests\Unit\Middleware;

use Tests\TestCase;
use App\Http\Middleware\RoleMiddleware;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Symfony\Component\HttpKernel\Exception\HttpException;

class RoleMiddlewareTest extends TestCase
{
    protected $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new RoleMiddleware();
        $this->createBasicRoles();
    }

    /**
     * Test middleware redirects unauthenticated users to login.
     */
    public function test_middleware_redirects_unauthenticated_users_to_login()
    {
        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'candidate');

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('login', $response->headers->get('Location'));
    }

    /**
     * Test middleware allows access for correct role.
     */
    public function test_middleware_allows_access_for_correct_role()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'candidate');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    /**
     * Test middleware allows access for multiple roles.
     */
    public function test_middleware_allows_access_for_multiple_roles()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'candidate', 'recruter');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    /**
     * Test middleware redirects admin to admin dashboard.
     */
    public function test_middleware_redirects_admin_to_admin_dashboard()
    {
        $adminRole = Role::where('slug', 'admin')->first();
        
        $user = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $adminRole->id,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'candidate');

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('admin', $response->headers->get('Location'));
    }

    /**
     * Test middleware redirects recruiter to recruiter dashboard.
     */
    public function test_middleware_redirects_recruiter_to_recruiter_dashboard()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'candidate');

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/dashboard', $response->headers->get('Location'));
    }

    /**
     * Test middleware redirects candidate to candidate dashboard.
     */
    public function test_middleware_redirects_candidate_to_candidate_dashboard()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'recruter');

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('candidats/dashboard', $response->headers->get('Location'));
    }

    /**
     * Test middleware with admin role accessing admin-only route.
     */
    public function test_middleware_admin_accessing_admin_route()
    {
        $adminRole = Role::where('slug', 'admin')->first();
        
        $user = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $adminRole->id,
        ]);

        Auth::login($user);

        $request = Request::create('/admin/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Admin Success');
        }, 'admin');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Admin Success', $response->getContent());
    }

    /**
     * Test middleware with multiple allowed roles.
     */
    public function test_middleware_with_multiple_allowed_roles()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        // Test candidate access
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        Auth::login($candidate);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'candidate', 'recruter');

        $this->assertEquals(200, $response->getStatusCode());

        // Test recruiter access
        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        Auth::login($recruiter);

        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'candidate', 'recruter');

        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * Test middleware throws 403 for unauthorized role when no redirect is applicable.
     */
    public function test_middleware_throws_403_for_unauthorized_role()
    {
        $this->expectException(HttpException::class);
        $this->expectExceptionMessage('Accès interdit');

        // Create a role that doesn't have a specific redirect
        Role::create(['name' => 'custom_role', 'slug' => 'custom_role']);
        $customRole = Role::where('slug', 'custom_role')->first();
        
        $user = User::create([
            'name' => 'Custom User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $customRole->id,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'candidate');
    }
}
