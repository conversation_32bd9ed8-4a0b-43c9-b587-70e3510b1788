<?php

namespace Tests\Unit\Middleware;

use Tests\TestCase;
use App\Http\Middleware\IsSubscribed;
use App\Models\User;
use App\Models\Role;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\ConfigGlobalApp;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class IsSubscribedTest extends TestCase
{
    protected $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new IsSubscribed();
        $this->createBasicRoles();
    }

    /**
     * Test middleware allows access for users with active subscription.
     */
    public function test_middleware_allows_access_for_users_with_active_subscription()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    /**
     * Test middleware redirects users without subscription to packages page.
     */
    public function test_middleware_redirects_users_without_subscription_to_packages_page()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/packages', $response->headers->get('Location'));
    }

    /**
     * Test middleware allows access during free trial period.
     */
    public function test_middleware_allows_access_during_free_trial_period()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Enable banner display
        ConfigGlobalApp::create([
            'name' => 'display_bandeau',
            'value' => true,
            'comment' => 'Display banner configuration',
        ]);

        // Set trial expiration to future date
        ConfigGlobalApp::create([
            'name' => 'day_free_after_publish',
            'value' => now()->addDays(15)->toDateTimeString(),
            'comment' => 'Free trial expiration date',
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    /**
     * Test middleware redirects when free trial has expired.
     */
    public function test_middleware_redirects_when_free_trial_has_expired()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Enable banner display
        ConfigGlobalApp::create([
            'name' => 'display_bandeau',
            'value' => true,
            'comment' => 'Display banner configuration',
        ]);

        // Set trial expiration to past date
        ConfigGlobalApp::create([
            'name' => 'day_free_after_publish',
            'value' => now()->subDays(5)->toDateTimeString(),
            'comment' => 'Free trial expiration date',
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/packages', $response->headers->get('Location'));
    }

    /**
     * Test middleware redirects when banner is disabled.
     */
    public function test_middleware_redirects_when_banner_is_disabled()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Disable banner display
        ConfigGlobalApp::create([
            'name' => 'display_bandeau',
            'value' => false,
            'comment' => 'Display banner configuration',
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/packages', $response->headers->get('Location'));
    }

    /**
     * Test middleware redirects when no trial configuration exists.
     */
    public function test_middleware_redirects_when_no_trial_configuration_exists()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Enable banner display but no trial config
        ConfigGlobalApp::create([
            'name' => 'display_bandeau',
            'value' => true,
            'comment' => 'Display banner configuration',
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/packages', $response->headers->get('Location'));
    }

    /**
     * Test middleware with canceled subscription.
     */
    public function test_middleware_with_canceled_subscription()
    {
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'canceled',
            'quantity' => 1,
            'ends_at' => now()->addDays(30),
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/packages', $response->headers->get('Location'));
    }

    /**
     * Test middleware redirects unauthenticated users.
     */
    public function test_middleware_redirects_unauthenticated_users()
    {
        $request = Request::create('/test', 'GET');
        
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        });

        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('recruter/packages', $response->headers->get('Location'));
    }
}
