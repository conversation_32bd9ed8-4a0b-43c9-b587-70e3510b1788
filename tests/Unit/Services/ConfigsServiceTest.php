<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\ConfigsService;

class ConfigsServiceTest extends TestCase
{
    /**
     * Test getMailConfig returns correct structure.
     */
    public function test_get_mail_config_returns_correct_structure()
    {
        $config = ConfigsService::getMailConfig();

        $this->assertIsArray($config);
        $this->assertArrayHasKey('default', $config);
        $this->assertArrayHas<PERSON>ey('mailers', $config);
        $this->assertArrayHasKey('from', $config);
        $this->assertArrayHasKey('markdown', $config);

        $this->assertEquals('smtp', $config['default']);
    }

    /**
     * Test getMailConfig SMTP configuration.
     */
    public function test_get_mail_config_smtp_configuration()
    {
        $config = ConfigsService::getMailConfig();

        $this->assertArrayHas<PERSON>ey('smtp', $config['mailers']);
        
        $smtpConfig = $config['mailers']['smtp'];
        $this->assertEquals('smtp', $smtpConfig['transport']);
        $this->assertEquals('mail.webagence-rte.site', $smtpConfig['host']);
        $this->assertEquals('465', $smtpConfig['port']);
        $this->assertEquals('ssl', $smtpConfig['encryption']);
        $this->assertEquals('<EMAIL>', $smtpConfig['username']);
        $this->assertEquals('3291treha@#Weba', $smtpConfig['password']);
        $this->assertNull($smtpConfig['timeout']);
    }

    /**
     * Test getMailConfig from address configuration.
     */
    public function test_get_mail_config_from_address()
    {
        $config = ConfigsService::getMailConfig();

        $this->assertArrayHasKey('from', $config);
        $this->assertEquals('<EMAIL>', $config['from']['address']);
        $this->assertEquals(env('APP_NAME'), $config['from']['name']);
    }

    /**
     * Test getMailConfig includes all required mailers.
     */
    public function test_get_mail_config_includes_all_mailers()
    {
        $config = ConfigsService::getMailConfig();

        $expectedMailers = ['smtp', 'ses', 'mailgun', 'postmark', 'sendmail', 'log', 'array', 'failover'];
        
        foreach ($expectedMailers as $mailer) {
            $this->assertArrayHasKey($mailer, $config['mailers']);
        }
    }

    /**
     * Test getCaptchaConfig returns correct structure.
     */
    public function test_get_captcha_config_returns_correct_structure()
    {
        $config = ConfigsService::getCaptchaConfig();

        $this->assertIsArray($config);
        $this->assertArrayHasKey('secret', $config);
        $this->assertArrayHasKey('sitekey', $config);
        $this->assertArrayHasKey('options', $config);
    }

    /**
     * Test getCaptchaConfig values.
     */
    public function test_get_captcha_config_values()
    {
        $config = ConfigsService::getCaptchaConfig();

        $this->assertEquals('6LcVClYqAAAAACs8p05tgkrfVm8m3cXG86Wt755I', $config['secret']);
        $this->assertEquals('6LcVClYqAAAAAOth0hTq1lafMhQidZSMrw9rSkGR', $config['sitekey']);
        $this->assertIsArray($config['options']);
        $this->assertEquals(30, $config['options']['timeout']);
    }

    /**
     * Test getStripeConfig returns correct structure.
     */
    public function test_get_stripe_config_returns_correct_structure()
    {
        $config = ConfigsService::getStripeConfig();

        $this->assertIsArray($config);
        $this->assertArrayHasKey('key', $config);
        $this->assertArrayHasKey('secret', $config);
        $this->assertArrayHasKey('webhook', $config);
        $this->assertArrayHasKey('currency', $config);
    }

    /**
     * Test getStripeConfig values.
     */
    public function test_get_stripe_config_values()
    {
        $config = ConfigsService::getStripeConfig();

        $this->assertEquals('pk_test_51OYqNsFKK6JoGdxmMWzT2SX9IciQGeQSItvJ8TxyoaCpVdrMs5dnpSCy2sSaygudhqbsnkznIavIu2l1hzmYwzaz00zTk4g7hM', $config['key']);
        $this->assertEquals('sk_test_51OYqNsFKK6JoGdxm411OjVQKO6Lv3q2Ta6sg2ry9G5PNZXnEYzpNDE43lVXp0hVHesYlxC3gzF2VYd6Lv4ELB7Sb00smdPudEY', $config['secret']);
        $this->assertEquals('chf', $config['currency']);
    }

    /**
     * Test getStripeConfig webhook configuration.
     */
    public function test_get_stripe_config_webhook()
    {
        $config = ConfigsService::getStripeConfig();

        $this->assertArrayHasKey('webhook', $config);
        $this->assertArrayHasKey('secret', $config['webhook']);
        $this->assertArrayHasKey('tolerance', $config['webhook']);
        
        $this->assertEquals('whsec_eFg0JkzZjPIhNwfSzaESsQFYnKx9MPqm', $config['webhook']['secret']);
        $this->assertEquals(300, $config['webhook']['tolerance']);
    }

    /**
     * Test getAdminMailConfig returns correct email.
     */
    public function test_get_admin_mail_config()
    {
        $adminEmail = ConfigsService::getAdminMailConfig();

        $this->assertIsString($adminEmail);
        $this->assertEquals('<EMAIL>', $adminEmail);
        $this->assertStringContainsString('@', $adminEmail);
        $this->assertTrue(filter_var($adminEmail, FILTER_VALIDATE_EMAIL) !== false);
    }

    /**
     * Test all methods are static.
     */
    public function test_all_methods_are_static()
    {
        $reflection = new \ReflectionClass(ConfigsService::class);
        $methods = $reflection->getMethods(\ReflectionMethod::IS_PUBLIC);

        foreach ($methods as $method) {
            if ($method->getDeclaringClass()->getName() === ConfigsService::class) {
                $this->assertTrue($method->isStatic(), "Method {$method->getName()} should be static");
            }
        }
    }

    /**
     * Test service methods return consistent data types.
     */
    public function test_service_methods_return_consistent_types()
    {
        // Test multiple calls return same structure
        $mailConfig1 = ConfigsService::getMailConfig();
        $mailConfig2 = ConfigsService::getMailConfig();
        $this->assertEquals($mailConfig1, $mailConfig2);

        $captchaConfig1 = ConfigsService::getCaptchaConfig();
        $captchaConfig2 = ConfigsService::getCaptchaConfig();
        $this->assertEquals($captchaConfig1, $captchaConfig2);

        $stripeConfig1 = ConfigsService::getStripeConfig();
        $stripeConfig2 = ConfigsService::getStripeConfig();
        $this->assertEquals($stripeConfig1, $stripeConfig2);

        $adminEmail1 = ConfigsService::getAdminMailConfig();
        $adminEmail2 = ConfigsService::getAdminMailConfig();
        $this->assertEquals($adminEmail1, $adminEmail2);
    }

    /**
     * Test mail config failover configuration.
     */
    public function test_mail_config_failover_configuration()
    {
        $config = ConfigsService::getMailConfig();

        $this->assertArrayHasKey('failover', $config['mailers']);
        $failoverConfig = $config['mailers']['failover'];
        
        $this->assertEquals('failover', $failoverConfig['transport']);
        $this->assertArrayHasKey('mailers', $failoverConfig);
        $this->assertContains('smtp', $failoverConfig['mailers']);
        $this->assertContains('log', $failoverConfig['mailers']);
    }

    /**
     * Test markdown configuration.
     */
    public function test_mail_config_markdown_configuration()
    {
        $config = ConfigsService::getMailConfig();

        $this->assertArrayHasKey('markdown', $config);
        $this->assertEquals('default', $config['markdown']['theme']);
        $this->assertIsArray($config['markdown']['paths']);
        $this->assertContains(resource_path('views/vendor/mail'), $config['markdown']['paths']);
    }

    /**
     * Test configuration values are not empty.
     */
    public function test_configuration_values_are_not_empty()
    {
        $mailConfig = ConfigsService::getMailConfig();
        $this->assertNotEmpty($mailConfig['mailers']['smtp']['host']);
        $this->assertNotEmpty($mailConfig['mailers']['smtp']['username']);
        $this->assertNotEmpty($mailConfig['from']['address']);

        $captchaConfig = ConfigsService::getCaptchaConfig();
        $this->assertNotEmpty($captchaConfig['secret']);
        $this->assertNotEmpty($captchaConfig['sitekey']);

        $stripeConfig = ConfigsService::getStripeConfig();
        $this->assertNotEmpty($stripeConfig['key']);
        $this->assertNotEmpty($stripeConfig['secret']);
        $this->assertNotEmpty($stripeConfig['webhook']['secret']);

        $adminEmail = ConfigsService::getAdminMailConfig();
        $this->assertNotEmpty($adminEmail);
    }
}
