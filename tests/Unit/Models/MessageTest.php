<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Support\Facades\Hash;

class MessageTest extends TestCase
{
    /**
     * Test message creation.
     */
    public function test_message_can_be_created()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Hello, I am interested in this position.',
            'is_read' => false,
        ]);

        $this->assertNotNull($message->id);
        $this->assertEquals($conversation->id, $message->conversation_id);
        $this->assertEquals($candidate->id, $message->sender_id);
        $this->assertEquals('Hello, I am interested in this position.', $message->content);
        $this->assertFalse($message->is_read);
    }

    /**
     * Test message conversation relationship.
     */
    public function test_message_conversation_relationship()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Test message',
            'is_read' => false,
        ]);

        $messageConversation = $message->conversation();
        $this->assertNotNull($messageConversation);
        $this->assertEquals($conversation->id, $messageConversation->id);
        $this->assertEquals($candidate->id, $messageConversation->user_one_id);
        $this->assertEquals($recruiter->id, $messageConversation->user_two_id);
    }

    /**
     * Test message sender relationship.
     */
    public function test_message_sender_relationship()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Test message',
            'is_read' => false,
        ]);

        $sender = $message->sender();
        $this->assertNotNull($sender);
        $this->assertEquals($candidate->id, $sender->id);
        $this->assertEquals('Jane Candidate', $sender->name);
        $this->assertEquals('<EMAIL>', $sender->email);
    }

    /**
     * Test message read status.
     */
    public function test_message_read_status()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        // Create unread message
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Test message',
            'is_read' => false,
        ]);

        $this->assertFalse($message->is_read);

        // Mark as read
        $message->update(['is_read' => true]);
        $this->assertTrue($message->fresh()->is_read);
    }

    /**
     * Test message with different senders.
     */
    public function test_message_with_different_senders()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        // Message from candidate
        $candidateMessage = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Message from candidate',
            'is_read' => false,
        ]);

        // Message from recruiter
        $recruiterMessage = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $recruiter->id,
            'content' => 'Message from recruiter',
            'is_read' => false,
        ]);

        $candidateSender = $candidateMessage->sender();
        $recruiterSender = $recruiterMessage->sender();

        $this->assertEquals($candidate->id, $candidateSender->id);
        $this->assertEquals('candidate', $candidateSender->getRoleSlugAttribute());

        $this->assertEquals($recruiter->id, $recruiterSender->id);
        $this->assertEquals('recruter', $recruiterSender->getRoleSlugAttribute());
    }

    /**
     * Test message content validation.
     */
    public function test_message_content_validation()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        // Test with long content
        $longContent = str_repeat('This is a long message. ', 100);
        
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => $longContent,
            'is_read' => false,
        ]);

        $this->assertEquals($longContent, $message->content);
        $this->assertNotNull($message->id);
    }
}
