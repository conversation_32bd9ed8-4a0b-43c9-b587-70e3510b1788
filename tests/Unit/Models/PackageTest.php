<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\Package;

class PackageTest extends TestCase
{
    /**
     * Test package creation with basic attributes.
     */
    public function test_package_can_be_created_with_basic_attributes()
    {
        $package = Package::create([
            'name' => 'Basic Package',
            'price' => 29.99,
            'currency' => 'EUR',
            'tax_included' => true,
            'features' => [
                'Access to candidate profiles',
                'Basic messaging',
                'Email support'
            ]
        ]);

        $this->assertNotNull($package->id);
        $this->assertEquals('Basic Package', $package->name);
        $this->assertEquals(29.99, $package->price);
        $this->assertEquals('EUR', $package->currency);
        $this->assertTrue($package->tax_included);
        $this->assertIsArray($package->features);
        $this->assertCount(3, $package->features);
    }

    /**
     * Test package features array casting.
     */
    public function test_package_features_array_casting()
    {
        $features = [
            'Unlimited candidate access',
            'Advanced search filters',
            'Priority support',
            'CV download',
            'Analytics dashboard'
        ];

        $package = Package::create([
            'name' => 'Premium Package',
            'price' => 99.99,
            'currency' => 'EUR',
            'tax_included' => false,
            'features' => $features
        ]);

        $this->assertIsArray($package->features);
        $this->assertEquals($features, $package->features);
        $this->assertCount(5, $package->features);
        $this->assertContains('Unlimited candidate access', $package->features);
        $this->assertContains('Analytics dashboard', $package->features);
    }

    /**
     * Test package tax_included boolean casting.
     */
    public function test_package_tax_included_boolean_casting()
    {
        // Test with true
        $packageWithTax = Package::create([
            'name' => 'Package With Tax',
            'price' => 50.00,
            'currency' => 'EUR',
            'tax_included' => true,
            'features' => ['Feature 1']
        ]);

        $this->assertTrue($packageWithTax->tax_included);
        $this->assertIsBool($packageWithTax->tax_included);

        // Test with false
        $packageWithoutTax = Package::create([
            'name' => 'Package Without Tax',
            'price' => 50.00,
            'currency' => 'EUR',
            'tax_included' => false,
            'features' => ['Feature 1']
        ]);

        $this->assertFalse($packageWithoutTax->tax_included);
        $this->assertIsBool($packageWithoutTax->tax_included);
    }

    /**
     * Test package with different currencies.
     */
    public function test_package_with_different_currencies()
    {
        $eurPackage = Package::create([
            'name' => 'EUR Package',
            'price' => 29.99,
            'currency' => 'EUR',
            'tax_included' => true,
            'features' => ['Feature 1']
        ]);

        $usdPackage = Package::create([
            'name' => 'USD Package',
            'price' => 34.99,
            'currency' => 'USD',
            'tax_included' => true,
            'features' => ['Feature 1']
        ]);

        $chfPackage = Package::create([
            'name' => 'CHF Package',
            'price' => 32.50,
            'currency' => 'CHF',
            'tax_included' => true,
            'features' => ['Feature 1']
        ]);

        $this->assertEquals('EUR', $eurPackage->currency);
        $this->assertEquals('USD', $usdPackage->currency);
        $this->assertEquals('CHF', $chfPackage->currency);
    }

    /**
     * Test package with zero price (free package).
     */
    public function test_package_with_zero_price()
    {
        $freePackage = Package::create([
            'name' => 'Free Package',
            'price' => 0.00,
            'currency' => 'EUR',
            'tax_included' => true,
            'features' => [
                'Limited access',
                'Basic support'
            ]
        ]);

        $this->assertEquals(0.00, $freePackage->price);
        $this->assertEquals('Free Package', $freePackage->name);
        $this->assertCount(2, $freePackage->features);
    }

    /**
     * Test package with high price.
     */
    public function test_package_with_high_price()
    {
        $enterprisePackage = Package::create([
            'name' => 'Enterprise Package',
            'price' => 999.99,
            'currency' => 'EUR',
            'tax_included' => false,
            'features' => [
                'Unlimited everything',
                'Dedicated account manager',
                'Custom integrations',
                'SLA guarantee',
                'White-label solution'
            ]
        ]);

        $this->assertEquals(999.99, $enterprisePackage->price);
        $this->assertEquals('Enterprise Package', $enterprisePackage->name);
        $this->assertFalse($enterprisePackage->tax_included);
        $this->assertCount(5, $enterprisePackage->features);
    }

    /**
     * Test package update.
     */
    public function test_package_update()
    {
        $package = Package::create([
            'name' => 'Original Package',
            'price' => 29.99,
            'currency' => 'EUR',
            'tax_included' => true,
            'features' => ['Feature 1', 'Feature 2']
        ]);

        $originalId = $package->id;

        // Update package
        $package->update([
            'name' => 'Updated Package',
            'price' => 39.99,
            'features' => ['Feature 1', 'Feature 2', 'Feature 3']
        ]);

        $updatedPackage = Package::find($originalId);
        $this->assertEquals('Updated Package', $updatedPackage->name);
        $this->assertEquals(39.99, $updatedPackage->price);
        $this->assertCount(3, $updatedPackage->features);
        $this->assertContains('Feature 3', $updatedPackage->features);
    }

    /**
     * Test package deletion.
     */
    public function test_package_deletion()
    {
        $package = Package::create([
            'name' => 'Package to Delete',
            'price' => 19.99,
            'currency' => 'EUR',
            'tax_included' => true,
            'features' => ['Feature 1']
        ]);

        $packageId = $package->id;
        $this->assertNotNull(Package::find($packageId));

        // Delete package
        $package->delete();

        $this->assertNull(Package::find($packageId));
    }

    /**
     * Test package with empty features array.
     */
    public function test_package_with_empty_features()
    {
        $package = Package::create([
            'name' => 'Package with No Features',
            'price' => 10.00,
            'currency' => 'EUR',
            'tax_included' => true,
            'features' => []
        ]);

        $this->assertIsArray($package->features);
        $this->assertCount(0, $package->features);
        $this->assertEmpty($package->features);
    }

    /**
     * Test package fillable attributes.
     */
    public function test_package_fillable_attributes()
    {
        $packageData = [
            'name' => 'Test Package',
            'price' => 49.99,
            'currency' => 'EUR',
            'tax_included' => true,
            'features' => ['Feature A', 'Feature B'],
            'non_fillable_field' => 'This should not be saved'
        ];

        $package = Package::create($packageData);

        $this->assertEquals('Test Package', $package->name);
        $this->assertEquals(49.99, $package->price);
        $this->assertEquals('EUR', $package->currency);
        $this->assertTrue($package->tax_included);
        $this->assertEquals(['Feature A', 'Feature B'], $package->features);
        
        // Non-fillable field should not be saved
        $this->assertNull($package->non_fillable_field ?? null);
    }
}
