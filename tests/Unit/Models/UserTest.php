<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Civility;
use App\Models\Phone;
use App\Models\Address;
use App\Models\UserProfession;
use App\Models\UserFieldActivity;
use App\Models\UserPermit;
use App\Models\File;
use App\Notifications\CustomVerifyEmail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\URL;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Laravel\Sanctum\HasApiTokens;
use Lara<PERSON>\Cashier\Billable;

class UserTest extends TestCase
{
    /**
     * Test user creation with basic attributes.
     */
    public function test_user_can_be_created_with_basic_attributes()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();

        $email = $this->getUniqueEmail('john.doe');
        $user = User::create([
            'name' => '<PERSON> Doe',
            'email' => $email,
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'is_suspend' => false,
        ]);

        $this->assertNotNull($user->id);
        $this->assertEquals('John Doe', $user->name);
        $this->assertEquals($email, $user->email);
        $this->assertFalse($user->is_suspend);
        $this->assertEquals($candidateRole->id, $user->role_id);
    }

    /**
     * Test user implements required interfaces and traits.
     */
    public function test_user_implements_required_interfaces_and_traits()
    {
        $user = new User();

        $this->assertInstanceOf(MustVerifyEmail::class, $user);
        $this->assertContains(HasApiTokens::class, class_uses_recursive($user));
        $this->assertContains(Billable::class, class_uses_recursive($user));
    }

    /**
     * Test user fillable attributes.
     */
    public function test_user_fillable_attributes()
    {
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();

        $userData = [
            'name' => 'Test User',
            'email' => $this->getUniqueEmail('test.user'),
            'password' => Hash::make('password123'),
            'is_suspend' => false,
            'role_id' => $candidateRole->id,
            'old_id' => 'old123',
            'instance_of' => 'test_instance',
            'reset_token' => 'reset123',
            'generated_password' => 'generated123',
            'registered_at' => now(),
            'email_verified_at' => now(),
            'sended_email_update_at' => now(),
            'non_fillable_field' => 'This should not be saved'
        ];

        $user = User::create($userData);

        $this->assertEquals('Test User', $user->name);
        $this->assertEquals($userData['email'], $user->email);
        $this->assertFalse($user->is_suspend);
        $this->assertEquals($candidateRole->id, $user->role_id);
        $this->assertEquals('old123', $user->old_id);
        $this->assertEquals('test_instance', $user->instance_of);
        $this->assertEquals('reset123', $user->reset_token);
        $this->assertEquals('generated123', $user->generated_password);
        $this->assertNotNull($user->registered_at);
        $this->assertNotNull($user->email_verified_at);
        $this->assertNotNull($user->sended_email_update_at);

        // Non-fillable field should not be saved
        $this->assertNull($user->non_fillable_field ?? null);
    }

    /**
     * Test user hidden attributes.
     */
    public function test_user_hidden_attributes()
    {
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'Test User',
            'email' => $this->getUniqueEmail('test.hidden'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'remember_token' => 'remember123',
        ]);

        $userArray = $user->toArray();

        $this->assertArrayNotHasKey('password', $userArray);
        $this->assertArrayNotHasKey('remember_token', $userArray);
    }

    /**
     * Test user casts.
     */
    public function test_user_casts()
    {
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'Test User',
            'email' => $this->getUniqueEmail('test.casts'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'email_verified_at' => '2024-01-01 12:00:00',
        ]);

        $this->assertInstanceOf(\Carbon\Carbon::class, $user->email_verified_at);
    }

    /**
     * Test getRoleSlugAttribute method.
     */
    public function test_get_role_slug_attribute()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();

        $candidate = User::create([
            'name' => 'Candidate User',
            'email' => $this->getUniqueEmail('candidate'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Recruiter User',
            'email' => $this->getUniqueEmail('recruiter'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $this->assertEquals('candidate', $candidate->getRoleSlugAttribute());
        $this->assertEquals('recruter', $recruiter->getRoleSlugAttribute());
    }

    /**
     * Test getDefaultNameFromEmail private method through user creation.
     */
    public function test_get_default_name_from_email()
    {
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'email' => $this->getUniqueEmail('john.doe'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // The name should be generated from email if not provided
        $this->assertNotNull($user->name);
    }

    /**
     * Test user role slug attribute.
     */
    public function test_user_role_slug_attribute()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => $this->getUniqueEmail('jane'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => $this->getUniqueEmail('bob'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $this->assertEquals('candidate', $candidate->getRoleSlugAttribute());
        $this->assertEquals('recruter', $recruiter->getRoleSlugAttribute());
    }

    /**
     * Test user fullname method for candidate.
     */
    public function test_user_fullname_for_candidate()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.fullname'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create civility for the user
        Civility::create([
            'user_id' => $user->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
        ]);

        $fullname = $user->fullname();
        $this->assertEquals('John Doe', $fullname);
    }

    /**
     * Test user fullname method for recruiter.
     */
    public function test_user_fullname_for_recruiter()
    {
        $this->createBasicRoles();
        
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Company User',
            'email' => $this->getUniqueEmail('company'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Create civility for the recruiter
        Civility::create([
            'user_id' => $user->id,
            'company_name' => 'Tech Corp',
        ]);

        $fullname = $user->fullname();
        $this->assertEquals('Tech Corp', $fullname);
    }

    /**
     * Test user firstname method.
     */
    public function test_user_firstname()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.firstname'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create civility for the user
        Civility::create([
            'user_id' => $user->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
        ]);

        $firstname = $user->firstname();
        $this->assertEquals('John', $firstname);
    }

    /**
     * Test user civility relationship.
     */
    public function test_user_civility_relationship()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.civility'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create civility for the user
        $civility = Civility::create([
            'user_id' => $user->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
        ]);

        $userCivility = $user->civility();
        $this->assertNotNull($userCivility);
        $this->assertEquals($civility->id, $userCivility->id);
        $this->assertEquals('John', $userCivility->first_name);
        $this->assertEquals('Doe', $userCivility->last_name);
    }

    /**
     * Test user phones relationship.
     */
    public function test_user_phones_relationship()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.phones'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create phones for the user
        $phone1 = Phone::create([
            'user_id' => $user->id,
            'phone' => '+41123456789',
        ]);

        $phone2 = Phone::create([
            'user_id' => $user->id,
            'phone' => '+41987654321',
        ]);

        $userPhones = $user->phones();
        $this->assertCount(2, $userPhones);
        $this->assertEquals($phone1->phone, $userPhones->first()->phone);
    }

    /**
     * Test user professions relationship.
     */
    public function test_user_professions_relationship()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.professions'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create user professions
        $userProfession = UserProfession::create([
            'user_id' => $user->id,
            'profession_id' => 'prof123',
        ]);

        $userProfessions = $user->userProfessions();
        $this->assertCount(1, $userProfessions);
        $this->assertEquals($userProfession->profession_id, $userProfessions->first()->profession_id);
    }

    /**
     * Test user field activities relationship.
     */
    public function test_user_field_activities_relationship()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.activities'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create user field activities
        $userActivity = UserFieldActivity::create([
            'user_id' => $user->id,
            'field_activity_id' => 'activity123',
        ]);

        $userActivities = $user->userFieldActivities();
        $this->assertCount(1, $userActivities);
        $this->assertEquals($userActivity->field_activity_id, $userActivities->first()->field_activity_id);
    }

    /**
     * Test user permits relationship.
     */
    public function test_user_permits_relationship()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.permits'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create user permits
        $userPermit = UserPermit::create([
            'user_id' => $user->id,
            'permit_id' => 'permit123',
        ]);

        $userPermits = $user->userPermits();
        $this->assertCount(1, $userPermits);
        $this->assertEquals($userPermit->permit_id, $userPermits->first()->permit_id);
    }

    /**
     * Test user profile photo relationship.
     */
    public function test_user_profile_photo_relationship()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.photo'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create profile photo file
        $profilePhoto = File::create([
            'user_id' => $user->_id,
            'usage' => 'profile_picture',
            'filename' => 'profile.jpg',
            'path' => '/uploads/profile.jpg',
        ]);

        $userPhoto = $user->profilePhoto();
        $this->assertNotNull($userPhoto);
        $this->assertEquals($profilePhoto->filename, $userPhoto->filename);
        $this->assertEquals('profile_picture', $userPhoto->usage);
    }

    /**
     * Test user address relationship.
     */
    public function test_user_address_relationship()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.address'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create address for the user
        $address = Address::create([
            'user_id' => $user->id,
            'street' => '123 Main St',
            'city' => 'Geneva',
            'postal_code' => '1200',
        ]);

        $userAddress = $user->address();
        $this->assertNotNull($userAddress);
        $this->assertEquals($address->street, $userAddress->street);
        $this->assertEquals($address->city, $userAddress->city);
    }

    /**
     * Test user getAddress method.
     */
    public function test_user_get_address()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.address'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Test without address - should return default
        $address = $user->getAddress();
        $this->assertInstanceOf(Address::class, $address);
        $this->assertEquals('', $address->name);
        $this->assertEquals(0.0, $address->lat);
        $this->assertEquals(0.0, $address->log);

        // Create actual address
        Address::create([
            'user_id' => $user->id,
            'name' => '123 Main St',
            'lat' => 40.7128,
            'log' => -74.0060,
        ]);

        $address = $user->getAddress();
        $this->assertEquals('123 Main St', $address->name);
        $this->assertEquals(40.7128, $address->lat);
        $this->assertEquals(-74.0060, $address->log);
    }

    /**
     * Test user getPhoneNumber method.
     */
    public function test_user_get_phone_number()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.phone'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Test without phone number
        $phone = $user->getPhoneNumber();
        $this->assertNull($phone);

        // Create phone number
        Phone::create([
            'user_id' => $user->id,
            'number' => '+1234567890',
        ]);

        $phone = $user->getPhoneNumber();
        $this->assertNotNull($phone);
        $this->assertEquals('+1234567890', $phone->number);
    }

    /**
     * Test user suspension.
     */
    public function test_user_suspension()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.suspension'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'is_suspend' => false,
        ]);

        $this->assertFalse($user->is_suspend);

        // Suspend user
        $user->update(['is_suspend' => true]);
        $this->assertTrue($user->fresh()->is_suspend);
    }

    /**
     * Test user email verification notification.
     */
    public function test_user_send_email_verification_notification()
    {
        Notification::fake();

        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.verify'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $user->sendEmailVerificationNotification();

        Notification::assertSentTo($user, CustomVerifyEmail::class);
    }

    /**
     * Test user native languages method.
     */
    public function test_user_native_languages()
    {
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.languages'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create civility with native languages
        Civility::create([
            'user_id' => $user->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'native_language' => ['fr', 'en'],
        ]);

        $nativeLanguages = $user->nativeLanguages();
        $this->assertIsArray($nativeLanguages);
        $this->assertContains('fr', $nativeLanguages);
        $this->assertContains('en', $nativeLanguages);
    }

    /**
     * Test user native languages method with null civility.
     */
    public function test_user_native_languages_with_null_civility()
    {
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.no.civility'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $nativeLanguages = $user->nativeLanguages();
        $this->assertIsArray($nativeLanguages);
        $this->assertEmpty($nativeLanguages);
    }

    /**
     * Test user password hashing.
     */
    public function test_user_password_is_hashed()
    {
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();

        $plainPassword = 'password123';
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.password'),
            'password' => Hash::make($plainPassword),
            'role_id' => $candidateRole->id,
        ]);

        $this->assertNotEquals($plainPassword, $user->password);
        $this->assertTrue(Hash::check($plainPassword, $user->password));
    }

    /**
     * Test user MongoDB connection.
     */
    public function test_user_uses_mongodb_connection()
    {
        $user = new User();
        $this->assertEquals('mongodb', $user->getConnectionName());
    }

    /**
     * Test user role relationship through role_id.
     */
    public function test_user_role_relationship()
    {
        $this->createBasicRoles();
        $candidateRole = Role::where('slug', 'candidate')->first();

        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.role'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $this->assertEquals($candidateRole->id, $user->role_id);
        $this->assertEquals('candidate', $user->getRoleSlugAttribute());
    }
}
