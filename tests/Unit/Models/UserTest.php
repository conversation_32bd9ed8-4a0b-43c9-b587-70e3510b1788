<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Civility;
use App\Models\Phone;
use App\Models\Address;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class UserTest extends TestCase
{
    /**
     * Test user creation with basic attributes.
     */
    public function test_user_can_be_created_with_basic_attributes()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $email = $this->getUniqueEmail('john.doe');
        $user = User::create([
            'name' => '<PERSON> Doe',
            'email' => $email,
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'is_suspend' => false,
        ]);

        $this->assertNotNull($user->id);
        $this->assertEquals('<PERSON>', $user->name);
        $this->assertEquals($email, $user->email);
        $this->assertFalse($user->is_suspend);
        $this->assertEquals($candidateRole->id, $user->role_id);
    }

    /**
     * Test user role slug attribute.
     */
    public function test_user_role_slug_attribute()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => $this->getUniqueEmail('jane'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => $this->getUniqueEmail('bob'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $this->assertEquals('candidate', $candidate->getRoleSlugAttribute());
        $this->assertEquals('recruter', $recruiter->getRoleSlugAttribute());
    }

    /**
     * Test user fullname method for candidate.
     */
    public function test_user_fullname_for_candidate()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.fullname'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create civility for the user
        Civility::create([
            'user_id' => $user->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
        ]);

        $fullname = $user->fullname();
        $this->assertEquals('John Doe', $fullname);
    }

    /**
     * Test user fullname method for recruiter.
     */
    public function test_user_fullname_for_recruiter()
    {
        $this->createBasicRoles();
        
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Company User',
            'email' => $this->getUniqueEmail('company'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Create civility for the recruiter
        Civility::create([
            'user_id' => $user->id,
            'company_name' => 'Tech Corp',
        ]);

        $fullname = $user->fullname();
        $this->assertEquals('Tech Corp', $fullname);
    }

    /**
     * Test user firstname method.
     */
    public function test_user_firstname()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.firstname'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Create civility for the user
        Civility::create([
            'user_id' => $user->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
        ]);

        $firstname = $user->firstname();
        $this->assertEquals('John', $firstname);
    }

    /**
     * Test user civility relationship.
     */
    public function test_user_civility_relationship()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.civility'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $civility = Civility::create([
            'user_id' => $user->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'sexe' => 'male',
        ]);

        $userCivility = $user->civility()->first();
        $this->assertNotNull($userCivility);
        $this->assertEquals('John', $userCivility->first_name);
        $this->assertEquals('Doe', $userCivility->last_name);
    }

    /**
     * Test user phones relationship.
     */
    public function test_user_phones_relationship()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.phones'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        Phone::create([
            'user_id' => $user->id,
            'number' => '+1234567890',
        ]);

        Phone::create([
            'user_id' => $user->id,
            'number' => '+0987654321',
        ]);

        $phones = $user->phones()->get();
        $this->assertCount(2, $phones);
    }

    /**
     * Test user getAddress method.
     */
    public function test_user_get_address()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.address'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Test without address - should return default
        $address = $user->getAddress();
        $this->assertInstanceOf(Address::class, $address);
        $this->assertEquals('', $address->name);
        $this->assertEquals(0.0, $address->lat);
        $this->assertEquals(0.0, $address->log);

        // Create actual address
        Address::create([
            'user_id' => $user->id,
            'name' => '123 Main St',
            'lat' => 40.7128,
            'log' => -74.0060,
        ]);

        $address = $user->getAddress();
        $this->assertEquals('123 Main St', $address->name);
        $this->assertEquals(40.7128, $address->lat);
        $this->assertEquals(-74.0060, $address->log);
    }

    /**
     * Test user getPhoneNumber method.
     */
    public function test_user_get_phone_number()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.phone'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        // Test without phone number
        $phone = $user->getPhoneNumber();
        $this->assertNull($phone);

        // Create phone number
        Phone::create([
            'user_id' => $user->id,
            'number' => '+1234567890',
        ]);

        $phone = $user->getPhoneNumber();
        $this->assertNotNull($phone);
        $this->assertEquals('+1234567890', $phone->number);
    }

    /**
     * Test user suspension.
     */
    public function test_user_suspension()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        
        $user = User::create([
            'name' => 'John Doe',
            'email' => $this->getUniqueEmail('john.suspension'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
            'is_suspend' => false,
        ]);

        $this->assertFalse($user->is_suspend);

        // Suspend user
        $user->update(['is_suspend' => true]);
        $this->assertTrue($user->fresh()->is_suspend);
    }
}
