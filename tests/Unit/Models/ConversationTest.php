<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Support\Facades\Hash;

class ConversationTest extends TestCase
{
    /**
     * Test conversation creation.
     */
    public function test_conversation_can_be_created()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $this->assertNotNull($conversation->id);
        $this->assertEquals($candidate->id, $conversation->user_one_id);
        $this->assertEquals($recruiter->id, $conversation->user_two_id);
    }

    /**
     * Test conversation userOne relationship.
     */
    public function test_conversation_user_one_relationship()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $userOne = $conversation->userOne();
        $this->assertNotNull($userOne);
        $this->assertEquals($candidate->id, $userOne->id);
        $this->assertEquals('Jane Candidate', $userOne->name);
    }

    /**
     * Test conversation userTwo relationship.
     */
    public function test_conversation_user_two_relationship()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $userTwo = $conversation->userTwo();
        $this->assertNotNull($userTwo);
        $this->assertEquals($recruiter->id, $userTwo->id);
        $this->assertEquals('Bob Recruiter', $userTwo->name);
    }

    /**
     * Test conversation messages relationship.
     */
    public function test_conversation_messages_relationship()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        // Create messages
        Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Hello, I am interested in this position.',
            'is_read' => false,
        ]);

        Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $recruiter->id,
            'content' => 'Thank you for your interest. Let\'s schedule an interview.',
            'is_read' => false,
        ]);

        $messages = $conversation->messages();
        $this->assertCount(2, $messages);
    }

    /**
     * Test conversation last_message method.
     */
    public function test_conversation_last_message()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        // Test without messages
        $lastMessage = $conversation->last_message();
        $this->assertNull($lastMessage);

        // Create first message
        $firstMessage = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'First message',
            'is_read' => false,
        ]);

        sleep(1); // Ensure different timestamps

        // Create second message
        $secondMessage = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $recruiter->id,
            'content' => 'Second message',
            'is_read' => false,
        ]);

        $lastMessage = $conversation->last_message();
        $this->assertNotNull($lastMessage);
        $this->assertEquals($secondMessage->id, $lastMessage->id);
        $this->assertEquals('Second message', $lastMessage->content);
    }

    /**
     * Test conversation with no messages.
     */
    public function test_conversation_with_no_messages()
    {
        $this->createBasicRoles();
        
        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $messages = $conversation->messages();
        $this->assertCount(0, $messages);

        $lastMessage = $conversation->last_message();
        $this->assertNull($lastMessage);
    }

    /**
     * Test conversation fillable attributes.
     */
    public function test_conversation_fillable_attributes()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();

        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('candidate.fillable'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('recruiter.fillable'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversationData = [
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
            'non_fillable_field' => 'This should not be saved'
        ];

        $conversation = Conversation::create($conversationData);

        $this->assertEquals($candidate->id, $conversation->user_one_id);
        $this->assertEquals($recruiter->id, $conversation->user_two_id);

        // Non-fillable field should not be saved
        $this->assertNull($conversation->non_fillable_field ?? null);
    }

    /**
     * Test conversation timestamps.
     */
    public function test_conversation_timestamps()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();

        $candidate = User::create([
            'name' => 'John Candidate',
            'email' => $this->getUniqueEmail('candidate.timestamps'),
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Jane Recruiter',
            'email' => $this->getUniqueEmail('recruiter.timestamps'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $this->assertNotNull($conversation->created_at);
        $this->assertNotNull($conversation->updated_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $conversation->created_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $conversation->updated_at);
    }
}
