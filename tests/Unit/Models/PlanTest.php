<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\Plan;

class PlanTest extends TestCase
{
    /**
     * Test plan creation with basic attributes.
     */
    public function test_plan_can_be_created_with_basic_attributes()
    {
        $plan = Plan::create([
            'name' => 'Monthly Premium Plan',
            'slug' => 'monthly-premium',
            'stripe_product_id' => 'prod_test123456',
            'stripe_price_id' => 'price_test123456',
            'price' => 29.99,
            'currency' => 'EUR',
            'show_name' => 'Premium - Monthly',
            'description_html' => '<p>This is a premium monthly plan with all features included.</p>',
            'duration_in_days' => 30,
        ]);

        $this->assertNotNull($plan->id);
        $this->assertEquals('Monthly Premium Plan', $plan->name);
        $this->assertEquals('monthly-premium', $plan->slug);
        $this->assertEquals('prod_test123456', $plan->stripe_product_id);
        $this->assertEquals('price_test123456', $plan->stripe_price_id);
        $this->assertEquals(29.99, $plan->price);
        $this->assertEquals('EUR', $plan->currency);
        $this->assertEquals('Premium - Monthly', $plan->show_name);
        $this->assertEquals('<p>This is a premium monthly plan with all features included.</p>', $plan->description_html);
        $this->assertEquals(30, $plan->duration_in_days);
    }

    /**
     * Test plan with different durations.
     */
    public function test_plan_with_different_durations()
    {
        // Monthly plan
        $monthlyPlan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly',
            'stripe_product_id' => 'prod_monthly',
            'stripe_price_id' => 'price_monthly',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        // Quarterly plan
        $quarterlyPlan = Plan::create([
            'name' => 'Quarterly Plan',
            'slug' => 'quarterly',
            'stripe_product_id' => 'prod_quarterly',
            'stripe_price_id' => 'price_quarterly',
            'price' => 79.99,
            'currency' => 'EUR',
            'duration_in_days' => 90,
        ]);

        // Yearly plan
        $yearlyPlan = Plan::create([
            'name' => 'Yearly Plan',
            'slug' => 'yearly',
            'stripe_product_id' => 'prod_yearly',
            'stripe_price_id' => 'price_yearly',
            'price' => 299.99,
            'currency' => 'EUR',
            'duration_in_days' => 365,
        ]);

        $this->assertEquals(30, $monthlyPlan->duration_in_days);
        $this->assertEquals(90, $quarterlyPlan->duration_in_days);
        $this->assertEquals(365, $yearlyPlan->duration_in_days);
    }

    /**
     * Test plan with different currencies.
     */
    public function test_plan_with_different_currencies()
    {
        $eurPlan = Plan::create([
            'name' => 'EUR Plan',
            'slug' => 'eur-plan',
            'stripe_product_id' => 'prod_eur',
            'stripe_price_id' => 'price_eur',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        $usdPlan = Plan::create([
            'name' => 'USD Plan',
            'slug' => 'usd-plan',
            'stripe_product_id' => 'prod_usd',
            'stripe_price_id' => 'price_usd',
            'price' => 34.99,
            'currency' => 'USD',
            'duration_in_days' => 30,
        ]);

        $chfPlan = Plan::create([
            'name' => 'CHF Plan',
            'slug' => 'chf-plan',
            'stripe_product_id' => 'prod_chf',
            'stripe_price_id' => 'price_chf',
            'price' => 32.50,
            'currency' => 'CHF',
            'duration_in_days' => 30,
        ]);

        $this->assertEquals('EUR', $eurPlan->currency);
        $this->assertEquals('USD', $usdPlan->currency);
        $this->assertEquals('CHF', $chfPlan->currency);
    }

    /**
     * Test plan with HTML description.
     */
    public function test_plan_with_html_description()
    {
        $htmlDescription = '<div class="plan-description">
            <h3>Premium Features</h3>
            <ul>
                <li>Unlimited candidate access</li>
                <li>Advanced search filters</li>
                <li>Priority support</li>
                <li>Analytics dashboard</li>
            </ul>
            <p><strong>Perfect for growing companies!</strong></p>
        </div>';

        $plan = Plan::create([
            'name' => 'Premium Plan',
            'slug' => 'premium',
            'stripe_product_id' => 'prod_premium',
            'stripe_price_id' => 'price_premium',
            'price' => 99.99,
            'currency' => 'EUR',
            'description_html' => $htmlDescription,
            'duration_in_days' => 30,
        ]);

        $this->assertEquals($htmlDescription, $plan->description_html);
        $this->assertStringContainsString('<h3>Premium Features</h3>', $plan->description_html);
        $this->assertStringContainsString('Unlimited candidate access', $plan->description_html);
    }

    /**
     * Test plan slug uniqueness.
     */
    public function test_plan_slug_uniqueness()
    {
        $plan1 = Plan::create([
            'name' => 'First Plan',
            'slug' => 'unique-plan',
            'stripe_product_id' => 'prod_first',
            'stripe_price_id' => 'price_first',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        $this->assertEquals('unique-plan', $plan1->slug);

        // Create another plan with different slug
        $plan2 = Plan::create([
            'name' => 'Second Plan',
            'slug' => 'another-unique-plan',
            'stripe_product_id' => 'prod_second',
            'stripe_price_id' => 'price_second',
            'price' => 39.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        $this->assertEquals('another-unique-plan', $plan2->slug);
        $this->assertNotEquals($plan1->slug, $plan2->slug);
    }

    /**
     * Test plan update.
     */
    public function test_plan_update()
    {
        $plan = Plan::create([
            'name' => 'Original Plan',
            'slug' => 'original-plan',
            'stripe_product_id' => 'prod_original',
            'stripe_price_id' => 'price_original',
            'price' => 29.99,
            'currency' => 'EUR',
            'show_name' => 'Original',
            'duration_in_days' => 30,
        ]);

        $originalId = $plan->id;

        // Update plan
        $plan->update([
            'name' => 'Updated Plan',
            'price' => 39.99,
            'show_name' => 'Updated Premium',
            'description_html' => '<p>This plan has been updated with new features.</p>',
        ]);

        $updatedPlan = Plan::find($originalId);
        $this->assertEquals('Updated Plan', $updatedPlan->name);
        $this->assertEquals(39.99, $updatedPlan->price);
        $this->assertEquals('Updated Premium', $updatedPlan->show_name);
        $this->assertEquals('<p>This plan has been updated with new features.</p>', $updatedPlan->description_html);
        
        // Unchanged fields should remain the same
        $this->assertEquals('original-plan', $updatedPlan->slug);
        $this->assertEquals('EUR', $updatedPlan->currency);
        $this->assertEquals(30, $updatedPlan->duration_in_days);
    }

    /**
     * Test plan deletion.
     */
    public function test_plan_deletion()
    {
        $plan = Plan::create([
            'name' => 'Plan to Delete',
            'slug' => 'plan-to-delete',
            'stripe_product_id' => 'prod_delete',
            'stripe_price_id' => 'price_delete',
            'price' => 19.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        $planId = $plan->id;
        $this->assertNotNull(Plan::find($planId));

        // Delete plan
        $plan->delete();

        $this->assertNull(Plan::find($planId));
    }

    /**
     * Test plan with zero price (free plan).
     */
    public function test_plan_with_zero_price()
    {
        $freePlan = Plan::create([
            'name' => 'Free Plan',
            'slug' => 'free-plan',
            'stripe_product_id' => 'prod_free',
            'stripe_price_id' => 'price_free',
            'price' => 0.00,
            'currency' => 'EUR',
            'show_name' => 'Free Tier',
            'description_html' => '<p>Free plan with limited features.</p>',
            'duration_in_days' => 30,
        ]);

        $this->assertEquals(0.00, $freePlan->price);
        $this->assertEquals('Free Plan', $freePlan->name);
        $this->assertEquals('Free Tier', $freePlan->show_name);
    }

    /**
     * Test plan fillable attributes.
     */
    public function test_plan_fillable_attributes()
    {
        $planData = [
            'name' => 'Test Plan',
            'slug' => 'test-plan',
            'stripe_product_id' => 'prod_test',
            'stripe_price_id' => 'price_test',
            'price' => 49.99,
            'currency' => 'EUR',
            'show_name' => 'Test Premium',
            'description_html' => '<p>Test description</p>',
            'duration_in_days' => 30,
            'non_fillable_field' => 'This should not be saved'
        ];

        $plan = Plan::create($planData);

        $this->assertEquals('Test Plan', $plan->name);
        $this->assertEquals('test-plan', $plan->slug);
        $this->assertEquals('prod_test', $plan->stripe_product_id);
        $this->assertEquals('price_test', $plan->stripe_price_id);
        $this->assertEquals(49.99, $plan->price);
        $this->assertEquals('EUR', $plan->currency);
        $this->assertEquals('Test Premium', $plan->show_name);
        $this->assertEquals('<p>Test description</p>', $plan->description_html);
        $this->assertEquals(30, $plan->duration_in_days);
        
        // Non-fillable field should not be saved
        $this->assertNull($plan->non_fillable_field ?? null);
    }
}
