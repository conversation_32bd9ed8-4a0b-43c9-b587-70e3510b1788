<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Plan;
use App\Models\Subscription;
use Illuminate\Support\Facades\Hash;

class SubscriptionTest extends TestCase
{
    /**
     * Test subscription creation.
     */
    public function test_subscription_can_be_created()
    {
        $this->createBasicRoles();
        
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        $subscription = Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        $this->assertNotNull($subscription->id);
        $this->assertEquals($user->id, $subscription->user_id);
        $this->assertEquals($plan->id, $subscription->plan_id);
        $this->assertEquals('sub_test123', $subscription->stripe_id);
        $this->assertEquals('active', $subscription->stripe_status);
        $this->assertEquals(1, $subscription->quantity);
    }

    /**
     * Test subscription with different statuses.
     */
    public function test_subscription_with_different_statuses()
    {
        $this->createBasicRoles();
        
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => $this->getUniqueEmail('bob.recruiter'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        // Create subscription and test different statuses
        $subscription = Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        // Test active status
        $this->assertEquals('active', $subscription->stripe_status);

        // Update to canceled status
        $subscription->update([
            'stripe_status' => 'canceled',
            'ends_at' => now()->addDays(30),
        ]);

        $subscription->refresh();
        $this->assertEquals('canceled', $subscription->stripe_status);
        $this->assertNotNull($subscription->ends_at);

        // Update to incomplete status
        $subscription->update([
            'stripe_status' => 'incomplete',
            'ends_at' => null,
        ]);

        $subscription->refresh();
        $this->assertEquals('incomplete', $subscription->stripe_status);
    }

    /**
     * Test subscription with trial period.
     */
    public function test_subscription_with_trial_period()
    {
        $this->createBasicRoles();
        
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        $subscription = Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_trial123',
            'stripe_status' => 'trialing',
            'quantity' => 1,
            'trial_ends_at' => now()->addDays(14),
        ]);

        $this->assertEquals('trialing', $subscription->stripe_status);
        $this->assertNotNull($subscription->trial_ends_at);
        $this->assertTrue($subscription->trial_ends_at->isFuture());
    }

    /**
     * Test subscription quantity.
     */
    public function test_subscription_quantity()
    {
        $this->createBasicRoles();
        
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        // Create another user for the second subscription
        $user2 = User::create([
            'name' => 'Test User 2',
            'email' => $this->getUniqueEmail('test.user2.quantity'),
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Single quantity subscription
        $singleSubscription = Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_single123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        // Multiple quantity subscription with different user
        $multipleSubscription = Subscription::create([
            'user_id' => $user2->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_multiple123',
            'stripe_status' => 'active',
            'quantity' => 5,
        ]);

        $this->assertEquals(1, $singleSubscription->quantity);
        $this->assertEquals(5, $multipleSubscription->quantity);
    }

    /**
     * Test subscription update.
     */
    public function test_subscription_update()
    {
        $this->createBasicRoles();
        
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        $subscription = Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        $originalId = $subscription->id;

        // Update subscription status
        $subscription->update([
            'stripe_status' => 'canceled',
            'ends_at' => now()->addDays(30),
        ]);

        $updatedSubscription = Subscription::find($originalId);
        $this->assertEquals('canceled', $updatedSubscription->stripe_status);
        $this->assertNotNull($updatedSubscription->ends_at);
    }

    /**
     * Test subscription deletion.
     */
    public function test_subscription_deletion()
    {
        $this->createBasicRoles();
        
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        $subscription = Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);

        $subscriptionId = $subscription->id;
        $this->assertNotNull(Subscription::find($subscriptionId));

        // Delete subscription
        $subscription->delete();

        $this->assertNull(Subscription::find($subscriptionId));
    }

    /**
     * Test subscription fillable attributes.
     */
    public function test_subscription_fillable_attributes()
    {
        $this->createBasicRoles();
        
        $recruiterRole = Role::where('slug', 'recruter')->first();
        
        $user = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        $subscriptionData = [
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
            'trial_ends_at' => now()->addDays(14),
            'ends_at' => null,
            'non_fillable_field' => 'This should not be saved'
        ];

        $subscription = Subscription::create($subscriptionData);

        $this->assertEquals($user->id, $subscription->user_id);
        $this->assertEquals($plan->id, $subscription->plan_id);
        $this->assertEquals('sub_test123', $subscription->stripe_id);
        $this->assertEquals('active', $subscription->stripe_status);
        $this->assertEquals(1, $subscription->quantity);
        $this->assertNotNull($subscription->trial_ends_at);
        $this->assertNull($subscription->ends_at);
        
        // Non-fillable field should not be saved
        $this->assertNull($subscription->non_fillable_field ?? null);
    }
}
