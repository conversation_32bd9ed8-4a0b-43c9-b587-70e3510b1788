<?php

namespace Tests\Unit\Mail;

use Tests\TestCase;
use App\Mail\ContactMail;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Attachment;

class ContactMailTest extends TestCase
{
    /**
     * Test ContactMail creation with basic data.
     */
    public function test_contact_mail_creation_with_basic_data()
    {
        $data = [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'message' => 'This is a test message',
        ];

        $mail = new ContactMail($data, false);

        $this->assertInstanceOf(ContactMail::class, $mail);
        $this->assertEquals($data, $mail->data);
        $this->assertFalse($mail->isRecruter);
        $this->assertNull($mail->subject);
        $this->assertNull($mail->attachment);
    }

    /**
     * Test ContactMail creation with recruiter data.
     */
    public function test_contact_mail_creation_with_recruiter_data()
    {
        $data = [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'message' => 'Looking for candidates',
        ];
        $subject = 'Job Opportunity';

        $mail = new ContactMail($data, true, $subject);

        $this->assertInstanceOf(ContactMail::class, $mail);
        $this->assertEquals($data, $mail->data);
        $this->assertTrue($mail->isRecruter);
        $this->assertEquals($subject, $mail->subject);
        $this->assertNull($mail->attachment);
    }

    /**
     * Test ContactMail creation with attachment.
     */
    public function test_contact_mail_creation_with_attachment()
    {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'message' => 'Please find my CV attached',
        ];
        $attachment = Attachment::fromPath('/path/to/cv.pdf');

        $mail = new ContactMail($data, false, null, $attachment);

        $this->assertInstanceOf(ContactMail::class, $mail);
        $this->assertEquals($data, $mail->data);
        $this->assertFalse($mail->isRecruter);
        $this->assertNull($mail->subject);
        $this->assertEquals($attachment, $mail->attachment);
    }

    /**
     * Test envelope method for regular contact.
     */
    public function test_envelope_method_for_regular_contact()
    {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'message' => 'This is a test message',
        ];

        $mail = new ContactMail($data, false);
        $envelope = $mail->envelope();

        $this->assertInstanceOf(Envelope::class, $envelope);
        
        // Access subject through reflection since it's protected
        $reflection = new \ReflectionClass($envelope);
        $subjectProperty = $reflection->getProperty('subject');
        $subjectProperty->setAccessible(true);
        $subject = $subjectProperty->getValue($envelope);
        
        $expectedSubject = 'Nouveau message de contact sur le site - ' . env('APP_NAME');
        $this->assertEquals($expectedSubject, $subject);
    }

    /**
     * Test envelope method for recruiter with custom subject.
     */
    public function test_envelope_method_for_recruiter_with_custom_subject()
    {
        $data = [
            'name' => 'Jane Recruiter',
            'email' => '<EMAIL>',
            'message' => 'Looking for candidates',
        ];
        $customSubject = 'Urgent: Software Developer Position';

        $mail = new ContactMail($data, true, $customSubject);
        $envelope = $mail->envelope();

        $this->assertInstanceOf(Envelope::class, $envelope);
        
        // Access subject through reflection
        $reflection = new \ReflectionClass($envelope);
        $subjectProperty = $reflection->getProperty('subject');
        $subjectProperty->setAccessible(true);
        $subject = $subjectProperty->getValue($envelope);
        
        $this->assertEquals($customSubject, $subject);
    }

    /**
     * Test envelope method for recruiter without custom subject.
     */
    public function test_envelope_method_for_recruiter_without_custom_subject()
    {
        $data = [
            'name' => 'Jane Recruiter',
            'email' => '<EMAIL>',
            'message' => 'Looking for candidates',
        ];

        $mail = new ContactMail($data, true); // No custom subject

        $envelope = $mail->envelope();

        $this->assertInstanceOf(Envelope::class, $envelope);
        
        // Access subject through reflection
        $reflection = new \ReflectionClass($envelope);
        $subjectProperty = $reflection->getProperty('subject');
        $subjectProperty->setAccessible(true);
        $subject = $subjectProperty->getValue($envelope);
        
        $expectedSubject = 'Nouveau message de contact sur le site - ' . env('APP_NAME');
        $this->assertEquals($expectedSubject, $subject);
    }

    /**
     * Test content method returns correct view.
     */
    public function test_content_method_returns_correct_view()
    {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'message' => 'This is a test message',
        ];

        $mail = new ContactMail($data, false);
        $content = $mail->content();

        $this->assertInstanceOf(Content::class, $content);
        
        // Access view through reflection
        $reflection = new \ReflectionClass($content);
        $viewProperty = $reflection->getProperty('view');
        $viewProperty->setAccessible(true);
        $view = $viewProperty->getValue($content);
        
        $this->assertEquals('mail.contact', $view);
    }

    /**
     * Test attachments method with no attachment.
     */
    public function test_attachments_method_with_no_attachment()
    {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'message' => 'This is a test message',
        ];

        $mail = new ContactMail($data, false);
        $attachments = $mail->attachments();

        $this->assertIsArray($attachments);
        $this->assertEmpty($attachments);
    }

    /**
     * Test attachments method with attachment.
     */
    public function test_attachments_method_with_attachment()
    {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'message' => 'Please find my CV attached',
        ];
        $attachment = Attachment::fromPath('/path/to/cv.pdf');

        $mail = new ContactMail($data, false, null, $attachment);
        $attachments = $mail->attachments();

        $this->assertIsArray($attachments);
        $this->assertCount(1, $attachments);
        $this->assertEquals($attachment, $attachments[0]);
    }

    /**
     * Test mail with empty data.
     */
    public function test_mail_with_empty_data()
    {
        $data = [];

        $mail = new ContactMail($data, false);

        $this->assertInstanceOf(ContactMail::class, $mail);
        $this->assertEquals($data, $mail->data);
        $this->assertFalse($mail->isRecruter);
    }

    /**
     * Test mail with special characters in data.
     */
    public function test_mail_with_special_characters_in_data()
    {
        $data = [
            'name' => 'José María Ñoño',
            'email' => '<EMAIL>',
            'message' => 'Hola! ¿Cómo está usted? Très bien, merci!',
        ];

        $mail = new ContactMail($data, false);

        $this->assertInstanceOf(ContactMail::class, $mail);
        $this->assertEquals($data, $mail->data);
    }

    /**
     * Test mail with long message.
     */
    public function test_mail_with_long_message()
    {
        $longMessage = str_repeat('This is a very long message. ', 100);
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'message' => $longMessage,
        ];

        $mail = new ContactMail($data, false);

        $this->assertInstanceOf(ContactMail::class, $mail);
        $this->assertEquals($data, $mail->data);
        $this->assertEquals($longMessage, $mail->data['message']);
    }

    /**
     * Test mail with HTML in message.
     */
    public function test_mail_with_html_in_message()
    {
        $htmlMessage = '<p>This is a <strong>bold</strong> message with <a href="http://example.com">link</a>.</p>';
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'message' => $htmlMessage,
        ];

        $mail = new ContactMail($data, false);

        $this->assertInstanceOf(ContactMail::class, $mail);
        $this->assertEquals($data, $mail->data);
        $this->assertEquals($htmlMessage, $mail->data['message']);
    }

    /**
     * Test mail constructor with all parameters.
     */
    public function test_mail_constructor_with_all_parameters()
    {
        $data = [
            'name' => 'Jane Recruiter',
            'email' => '<EMAIL>',
            'message' => 'Looking for developers',
            'company' => 'Tech Corp',
            'phone' => '+1234567890',
        ];
        $isRecruter = true;
        $subject = 'Developer Position Available';
        $attachment = Attachment::fromPath('/path/to/job-description.pdf');

        $mail = new ContactMail($data, $isRecruter, $subject, $attachment);

        $this->assertInstanceOf(ContactMail::class, $mail);
        $this->assertEquals($data, $mail->data);
        $this->assertTrue($mail->isRecruter);
        $this->assertEquals($subject, $mail->subject);
        $this->assertEquals($attachment, $mail->attachment);
    }
}
